#!/usr/bin/env python3
"""
Test script for Hidden Desktop functionality
Tests desktop creation, application launching, and screen capture.
"""

import sys
import time
import logging
from windows_desktop_manager import WindowsDesktopManager
from hidden_desktop_apps import HiddenDesktopAppLauncher

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_desktop_creation():
    """Test hidden desktop creation"""
    print("=" * 60)
    print("Testing Hidden Desktop Creation")
    print("=" * 60)
    
    try:
        # Check if running on Windows
        if sys.platform != 'win32':
            print("❌ This test requires Windows")
            return False
        
        # Create desktop manager
        desktop_manager = WindowsDesktopManager()
        
        # Create hidden desktop
        print("1. Creating hidden desktop...")
        success = desktop_manager.create_hidden_desktop("TestDesktop")
        if success:
            print("   ✓ Hidden desktop created successfully")
        else:
            print("   ❌ Failed to create hidden desktop")
            return False
        
        # Switch to hidden desktop
        print("2. Switching to hidden desktop...")
        success = desktop_manager.switch_to_hidden_desktop()
        if success:
            print("   ✓ Switched to hidden desktop successfully")
        else:
            print("   ❌ Failed to switch to hidden desktop")
            return False
        
        # Switch back to original desktop
        print("3. Switching back to original desktop...")
        success = desktop_manager.switch_to_original_desktop()
        if success:
            print("   ✓ Switched back to original desktop successfully")
        else:
            print("   ❌ Failed to switch back to original desktop")
            return False
        
        # Cleanup
        desktop_manager.cleanup()
        print("   ✓ Desktop cleaned up successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Desktop creation test failed: {e}")
        return False

def test_application_launching():
    """Test application launching on hidden desktop"""
    print("\n" + "=" * 60)
    print("Testing Application Launching")
    print("=" * 60)
    
    try:
        # Create desktop manager
        desktop_manager = WindowsDesktopManager()
        desktop_manager.create_hidden_desktop("TestAppDesktop")
        
        # Create app launcher
        app_launcher = HiddenDesktopAppLauncher(desktop_manager)
        
        print("1. Testing Explorer launch...")
        pid = app_launcher.launch_explorer()
        if pid:
            print(f"   ✓ Explorer launched successfully (PID: {pid})")
        else:
            print("   ⚠ Explorer launch failed or not found")
        
        print("2. Testing Notepad launch...")
        pid = app_launcher.launch_notepad()
        if pid:
            print(f"   ✓ Notepad launched successfully (PID: {pid})")
        else:
            print("   ⚠ Notepad launch failed")
        
        print("3. Testing Calculator launch...")
        pid = app_launcher.launch_calculator()
        if pid:
            print(f"   ✓ Calculator launched successfully (PID: {pid})")
        else:
            print("   ⚠ Calculator launch failed")
        
        print("4. Testing browser detection...")
        browsers = app_launcher.find_installed_browsers()
        if browsers:
            print(f"   ✓ Found browsers: {[name for name, path in browsers]}")
            
            # Try to launch default browser
            pid = app_launcher.launch_default_browser()
            if pid:
                print(f"   ✓ Default browser launched successfully (PID: {pid})")
            else:
                print("   ⚠ Default browser launch failed")
        else:
            print("   ⚠ No browsers found")
        
        # Show launched applications
        launched_apps = app_launcher.get_launched_applications()
        print(f"5. Total applications launched: {len(launched_apps)}")
        for app_name, pid in launched_apps:
            print(f"   - {app_name}: PID {pid}")
        
        # Wait a moment for applications to start
        print("6. Waiting for applications to initialize...")
        time.sleep(3)
        
        # Cleanup
        desktop_manager.cleanup()
        print("   ✓ Desktop cleaned up successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Application launching test failed: {e}")
        return False

def test_screen_capture():
    """Test screen capture from hidden desktop"""
    print("\n" + "=" * 60)
    print("Testing Screen Capture")
    print("=" * 60)
    
    try:
        # Create desktop manager
        desktop_manager = WindowsDesktopManager()
        desktop_manager.create_hidden_desktop("TestCaptureDesktop")
        
        # Launch some applications first
        app_launcher = HiddenDesktopAppLauncher(desktop_manager)
        app_launcher.launch_explorer()
        app_launcher.launch_notepad()
        
        print("1. Waiting for applications to start...")
        time.sleep(3)
        
        print("2. Capturing screenshot from hidden desktop...")
        screenshot = desktop_manager.capture_desktop_screenshot(800, 600)
        
        if screenshot:
            print(f"   ✓ Screenshot captured successfully")
            print(f"   - Size: {screenshot.size}")
            print(f"   - Mode: {screenshot.mode}")
            
            # Save screenshot for verification
            screenshot.save("hidden_desktop_test.png")
            print("   ✓ Screenshot saved as 'hidden_desktop_test.png'")
            
        else:
            print("   ❌ Failed to capture screenshot")
            return False
        
        # Test multiple captures
        print("3. Testing multiple captures...")
        for i in range(3):
            screenshot = desktop_manager.capture_desktop_screenshot(640, 480)
            if screenshot:
                print(f"   ✓ Capture {i+1}/3 successful")
            else:
                print(f"   ❌ Capture {i+1}/3 failed")
                return False
            time.sleep(0.5)
        
        # Cleanup
        desktop_manager.cleanup()
        print("   ✓ Desktop cleaned up successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Screen capture test failed: {e}")
        return False

def test_input_events():
    """Test input event handling on hidden desktop"""
    print("\n" + "=" * 60)
    print("Testing Input Events")
    print("=" * 60)
    
    try:
        # Create desktop manager
        desktop_manager = WindowsDesktopManager()
        desktop_manager.create_hidden_desktop("TestInputDesktop")
        
        # Launch Notepad for testing
        app_launcher = HiddenDesktopAppLauncher(desktop_manager)
        app_launcher.launch_notepad()
        
        print("1. Waiting for Notepad to start...")
        time.sleep(2)
        
        print("2. Testing mouse events...")
        # Test mouse move
        desktop_manager.send_input_to_desktop({
            'type': 'mouse_move',
            'x': 100,
            'y': 100
        })
        print("   ✓ Mouse move event sent")
        
        # Test mouse click
        desktop_manager.send_input_to_desktop({
            'type': 'mouse_click',
            'x': 100,
            'y': 100,
            'button': 'left'
        })
        print("   ✓ Mouse click event sent")
        
        print("3. Testing keyboard events...")
        # Test text typing
        desktop_manager.send_input_to_desktop({
            'type': 'key_type',
            'text': 'Hello Hidden Desktop!'
        })
        print("   ✓ Text typing event sent")
        
        # Test key press
        desktop_manager.send_input_to_desktop({
            'type': 'key_press',
            'key': 'enter'
        })
        print("   ✓ Key press event sent")
        
        print("4. Waiting for events to process...")
        time.sleep(1)
        
        # Capture screenshot to see if input worked
        screenshot = desktop_manager.capture_desktop_screenshot(800, 600)
        if screenshot:
            screenshot.save("hidden_desktop_input_test.png")
            print("   ✓ Screenshot after input saved as 'hidden_desktop_input_test.png'")
        
        # Cleanup
        desktop_manager.cleanup()
        print("   ✓ Desktop cleaned up successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Input events test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Hidden Desktop Functionality Test Suite")
    print("=" * 60)
    
    # Check Windows requirement
    if sys.platform != 'win32':
        print("❌ This test suite requires Windows")
        return
    
    tests = [
        ("Desktop Creation", test_desktop_creation),
        ("Application Launching", test_application_launching),
        ("Screen Capture", test_screen_capture),
        ("Input Events", test_input_events)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Hidden desktop functionality is working.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")

if __name__ == '__main__':
    main()
