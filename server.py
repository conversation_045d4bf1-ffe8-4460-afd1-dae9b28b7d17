#!/usr/bin/env python3
"""
Hidden Desktop Server - Simplified Implementation
Captures desktop screenshots and handles remote input events.
"""

import socket
import threading
import time
import json
import struct
import io
from PIL import Image, ImageGrab
import pyautogui
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HiddenDesktopServer:
    def __init__(self, host='127.0.0.1', desktop_port=4043, events_port=4044):
        self.host = host
        self.desktop_port = desktop_port
        self.events_port = events_port
        self.running = False
        self.clients = []
        self.desktop_socket = None
        self.events_socket = None
        
        # Screen capture settings
        self.screen_width = 1920
        self.screen_height = 1080
        self.image_quality = 80
        self.capture_fps = 30
        
        # Disable pyautogui failsafe
        pyautogui.FAILSAFE = False
        
    def start(self):
        """Start the server with both desktop and events sockets"""
        logger.info(f"Starting Hidden Desktop Server on {self.host}")
        logger.info(f"Desktop port: {self.desktop_port}, Events port: {self.events_port}")
        
        self.running = True
        
        # Start desktop server thread
        desktop_thread = threading.Thread(target=self._start_desktop_server, daemon=True)
        desktop_thread.start()
        
        # Start events server thread  
        events_thread = threading.Thread(target=self._start_events_server, daemon=True)
        events_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down server...")
            self.stop()
    
    def stop(self):
        """Stop the server"""
        self.running = False
        if self.desktop_socket:
            self.desktop_socket.close()
        if self.events_socket:
            self.events_socket.close()
    
    def _start_desktop_server(self):
        """Start the desktop image streaming server"""
        self.desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.desktop_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.desktop_socket.bind((self.host, self.desktop_port))
            self.desktop_socket.listen(5)
            logger.info(f"Desktop server listening on {self.host}:{self.desktop_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.desktop_socket.accept()
                    logger.info(f"Desktop client connected from {addr}")
                    
                    # Handle desktop client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_desktop_client, 
                        args=(client_socket, addr),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"Desktop server error: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to start desktop server: {e}")
    
    def _start_events_server(self):
        """Start the input events server"""
        self.events_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.events_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.events_socket.bind((self.host, self.events_port))
            self.events_socket.listen(5)
            logger.info(f"Events server listening on {self.host}:{self.events_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.events_socket.accept()
                    logger.info(f"Events client connected from {addr}")
                    
                    # Handle events client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_events_client,
                        args=(client_socket, addr),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"Events server error: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to start events server: {e}")
    
    def _handle_desktop_client(self, client_socket, addr):
        """Handle desktop image streaming for a client"""
        try:
            # Send initial screen info
            screen_info = {
                'width': self.screen_width,
                'height': self.screen_height,
                'fps': self.capture_fps
            }
            self._send_json(client_socket, screen_info)
            
            last_capture_time = 0
            frame_interval = 1.0 / self.capture_fps
            
            while self.running:
                current_time = time.time()
                if current_time - last_capture_time >= frame_interval:
                    try:
                        # Capture screenshot
                        screenshot = ImageGrab.grab()
                        
                        # Resize if needed
                        if screenshot.size != (self.screen_width, self.screen_height):
                            screenshot = screenshot.resize((self.screen_width, self.screen_height), Image.Resampling.LANCZOS)
                        
                        # Convert to JPEG bytes
                        img_buffer = io.BytesIO()
                        screenshot.save(img_buffer, format='JPEG', quality=self.image_quality)
                        img_data = img_buffer.getvalue()
                        
                        # Send image data
                        self._send_image_data(client_socket, img_data)
                        
                        last_capture_time = current_time
                        
                    except Exception as e:
                        logger.error(f"Error capturing/sending screenshot: {e}")
                        break
                else:
                    time.sleep(0.001)  # Small sleep to prevent busy waiting
                    
        except Exception as e:
            logger.error(f"Desktop client {addr} error: {e}")
        finally:
            client_socket.close()
            logger.info(f"Desktop client {addr} disconnected")
    
    def _handle_events_client(self, client_socket, addr):
        """Handle input events from a client"""
        try:
            while self.running:
                try:
                    # Receive event data
                    event_data = self._receive_json(client_socket)
                    if not event_data:
                        break
                        
                    self._process_input_event(event_data)
                    
                except Exception as e:
                    logger.error(f"Error processing event from {addr}: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Events client {addr} error: {e}")
        finally:
            client_socket.close()
            logger.info(f"Events client {addr} disconnected")
    
    def _process_input_event(self, event_data):
        """Process input events and apply them to the system"""
        try:
            event_type = event_data.get('type')
            
            if event_type == 'mouse_move':
                x, y = event_data['x'], event_data['y']
                pyautogui.moveTo(x, y)
                
            elif event_type == 'mouse_click':
                x, y = event_data['x'], event_data['y']
                button = event_data.get('button', 'left')
                pyautogui.click(x, y, button=button)
                
            elif event_type == 'mouse_drag':
                x1, y1 = event_data['x1'], event_data['y1']
                x2, y2 = event_data['x2'], event_data['y2']
                button = event_data.get('button', 'left')
                pyautogui.drag(x1, y1, x2, y2, button=button)
                
            elif event_type == 'key_press':
                key = event_data['key']
                pyautogui.press(key)
                
            elif event_type == 'key_type':
                text = event_data['text']
                pyautogui.write(text)
                
            elif event_type == 'scroll':
                x, y = event_data['x'], event_data['y']
                clicks = event_data.get('clicks', 1)
                pyautogui.scroll(clicks, x=x, y=y)
                
        except Exception as e:
            logger.error(f"Error processing input event: {e}")
    
    def _send_json(self, socket, data):
        """Send JSON data over socket"""
        json_data = json.dumps(data).encode('utf-8')
        data_length = len(json_data)
        socket.sendall(struct.pack('!I', data_length))
        socket.sendall(json_data)
    
    def _receive_json(self, socket):
        """Receive JSON data from socket"""
        # First receive the length
        length_data = socket.recv(4)
        if len(length_data) != 4:
            return None
        data_length = struct.unpack('!I', length_data)[0]
        
        # Then receive the JSON data
        json_data = b''
        while len(json_data) < data_length:
            chunk = socket.recv(data_length - len(json_data))
            if not chunk:
                return None
            json_data += chunk
        
        return json.loads(json_data.decode('utf-8'))
    
    def _send_image_data(self, socket, img_data):
        """Send image data over socket"""
        data_length = len(img_data)
        socket.sendall(struct.pack('!I', data_length))
        socket.sendall(img_data)

if __name__ == '__main__':
    server = HiddenDesktopServer()
    server.start()
