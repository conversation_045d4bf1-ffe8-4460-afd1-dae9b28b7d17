#!/usr/bin/env python3
"""
Test input events to the hidden desktop server
"""

import socket
import json
import struct
import time

def send_json(sock, data):
    """Send JSON data over socket"""
    json_data = json.dumps(data).encode('utf-8')
    data_length = len(json_data)
    sock.sendall(struct.pack('!I', data_length))
    sock.sendall(json_data)

def test_input_events():
    """Test sending input events to the hidden desktop"""
    print("Testing input events to hidden desktop...")
    
    try:
        # Connect to events server
        events_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        events_socket.connect(('127.0.0.1', 4044))
        print("✓ Connected to events server")
        
        # Test mouse move
        print("Sending mouse move event...")
        send_json(events_socket, {
            'type': 'mouse_move',
            'x': 200,
            'y': 200
        })
        time.sleep(0.5)
        
        # Test mouse click
        print("Sending mouse click event...")
        send_json(events_socket, {
            'type': 'mouse_click',
            'x': 200,
            'y': 200,
            'button': 'left'
        })
        time.sleep(0.5)
        
        # Test key typing
        print("Sending text typing event...")
        send_json(events_socket, {
            'type': 'key_type',
            'text': 'Hello Hidden Desktop!'
        })
        time.sleep(0.5)
        
        # Test key press
        print("Sending key press event...")
        send_json(events_socket, {
            'type': 'key_press',
            'key': 'enter'
        })
        time.sleep(0.5)
        
        events_socket.close()
        print("✓ All input events sent successfully")
        
    except Exception as e:
        print(f"✗ Input events test failed: {e}")

if __name__ == '__main__':
    test_input_events()
