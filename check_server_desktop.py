#!/usr/bin/env python3
"""
Check the actual hidden desktop created by the server
"""

import ctypes
from ctypes import wintypes, windll
import time
import re

def find_server_desktop_name():
    """Find the desktop name used by the server from logs"""
    try:
        # Look for the most recent server log to find desktop name
        import subprocess
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'], 
                              capture_output=True, text=True)
        
        # For now, use a pattern that matches the server's desktop naming
        # The server uses format: HiddenDesktop_<8-char-hex>
        return "HiddenDesktop_a7da6248"  # From the server logs we saw
    except:
        return None

def check_server_hidden_desktop():
    """Check the hidden desktop created by the server"""
    
    user32 = windll.user32
    kernel32 = windll.kernel32
    
    print("Checking Server's Hidden Desktop")
    print("=" * 40)
    
    # Get current desktop
    current_thread_id = kernel32.GetCurrentThreadId()
    original_desktop = user32.GetThreadDesktop(current_thread_id)
    print(f"Original desktop handle: {original_desktop}")
    
    # Try to find the server's desktop name
    desktop_name = find_server_desktop_name()
    if not desktop_name:
        print("✗ Could not determine server desktop name")
        return
    
    print(f"Looking for server desktop: {desktop_name}")
    
    # Try to open the existing desktop
    hidden_desktop = user32.OpenDesktopA(
        desktop_name.encode('ascii'), 0, False, 0x10000000  # GENERIC_ALL
    )
    
    if not hidden_desktop:
        error_code = kernel32.GetLastError()
        print(f"✗ Failed to open server desktop. Error: {error_code}")
        print("This might mean the server is not running or using a different name.")
        return
    
    print(f"✓ Successfully opened server desktop: {hidden_desktop}")
    
    try:
        # Switch to the server's hidden desktop
        switch_success = user32.SetThreadDesktop(hidden_desktop)
        
        if switch_success:
            print("✓ Successfully switched to server's hidden desktop")
            
            # Enumerate windows on the server's hidden desktop
            hidden_windows = []
            
            def enum_hidden_proc(hwnd, lParam):
                if user32.IsWindowVisible(hwnd):
                    length = user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        user32.GetWindowTextW(hwnd, buffer, length + 1)
                        window_title = buffer.value
                        if window_title and len(window_title.strip()) > 0:
                            # Get window rect for more info
                            rect = wintypes.RECT()
                            user32.GetWindowRect(hwnd, ctypes.byref(rect))
                            width = rect.right - rect.left
                            height = rect.bottom - rect.top
                            hidden_windows.append((hwnd, window_title, width, height))
                return True
            
            WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            enum_proc = WNDENUMPROC(enum_hidden_proc)
            user32.EnumWindows(enum_proc, 0)
            
            print(f"\nFound {len(hidden_windows)} windows on server's hidden desktop:")
            for hwnd, title, width, height in hidden_windows:
                print(f"  - {title}")
                print(f"    Handle: {hwnd}, Size: {width}x{height}")
            
            if len(hidden_windows) > 0:
                print(f"\n🎉 SUCCESS: Server's hidden desktop has {len(hidden_windows)} windows!")
                print("The applications are properly isolated on the hidden desktop.")
                
                # Look for specific applications
                app_types = {
                    'explorer': [w for w in hidden_windows if 'explorer' in w[1].lower() or 'file' in w[1].lower()],
                    'taskbar': [w for w in hidden_windows if 'taskbar' in w[1].lower() or 'shell_traywnd' in w[1].lower()],
                    'notepad': [w for w in hidden_windows if 'notepad' in w[1].lower()],
                    'chrome': [w for w in hidden_windows if 'chrome' in w[1].lower()],
                    'calculator': [w for w in hidden_windows if 'calculator' in w[1].lower()]
                }
                
                print("\nApplication breakdown:")
                for app_type, windows in app_types.items():
                    if windows:
                        print(f"  ✓ {app_type.title()}: {len(windows)} window(s)")
                        for w in windows:
                            print(f"    - {w[1]}")
                    else:
                        print(f"  - {app_type.title()}: Not found")
                
            else:
                print("✗ No windows found on server's hidden desktop")
                print("This suggests the applications may not be launching properly.")
            
            # Switch back to original desktop
            user32.SetThreadDesktop(original_desktop)
            print("\n✓ Switched back to original desktop")
            
        else:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to switch to server's hidden desktop. Error: {error_code}")
    
    finally:
        # Clean up
        user32.CloseDesktop(hidden_desktop)
        print("✓ Server desktop handle closed")

def test_print_window_on_server_desktop():
    """Test PrintWindow functionality on server's hidden desktop"""
    
    user32 = windll.user32
    gdi32 = windll.gdi32
    kernel32 = windll.kernel32
    
    print("\n" + "=" * 40)
    print("Testing PrintWindow on Server Desktop")
    print("=" * 40)
    
    desktop_name = find_server_desktop_name()
    if not desktop_name:
        print("✗ Could not determine server desktop name")
        return
    
    # Open server desktop
    hidden_desktop = user32.OpenDesktopA(
        desktop_name.encode('ascii'), 0, False, 0x10000000
    )
    
    if not hidden_desktop:
        print("✗ Could not open server desktop")
        return
    
    try:
        # Get current desktop for restoration
        original_desktop = user32.GetThreadDesktop(kernel32.GetCurrentThreadId())
        
        # Switch to server desktop
        if user32.SetThreadDesktop(hidden_desktop):
            print("✓ Switched to server desktop for PrintWindow test")
            
            # Find a window to test with
            test_windows = []
            
            def find_test_window(hwnd, lParam):
                if user32.IsWindowVisible(hwnd):
                    length = user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        user32.GetWindowTextW(hwnd, buffer, length + 1)
                        window_title = buffer.value
                        if window_title and len(window_title.strip()) > 0:
                            test_windows.append((hwnd, window_title))
                return True
            
            WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            enum_proc = WNDENUMPROC(find_test_window)
            user32.EnumWindows(enum_proc, 0)
            
            if test_windows:
                test_hwnd, test_title = test_windows[0]
                print(f"Testing PrintWindow on: {test_title}")
                
                # Get window rect
                rect = wintypes.RECT()
                if user32.GetWindowRect(test_hwnd, ctypes.byref(rect)):
                    width = rect.right - rect.left
                    height = rect.bottom - rect.top
                    print(f"Window size: {width}x{height}")
                    
                    if width > 0 and height > 0:
                        # Create DCs and test PrintWindow
                        hdc = user32.GetDC(None)
                        if hdc:
                            mem_dc = gdi32.CreateCompatibleDC(hdc)
                            if mem_dc:
                                bitmap = gdi32.CreateCompatibleBitmap(hdc, width, height)
                                if bitmap:
                                    gdi32.SelectObject(mem_dc, bitmap)
                                    
                                    # Test PrintWindow
                                    success = user32.PrintWindow(test_hwnd, mem_dc, 0)
                                    if success:
                                        print("✓ PrintWindow succeeded on server desktop!")
                                    else:
                                        print("✗ PrintWindow failed on server desktop")
                                    
                                    gdi32.DeleteObject(bitmap)
                                gdi32.DeleteDC(mem_dc)
                            user32.ReleaseDC(None, hdc)
                    else:
                        print("✗ Window has invalid size")
                else:
                    print("✗ Could not get window rect")
            else:
                print("✗ No windows found to test with")
            
            # Switch back
            user32.SetThreadDesktop(original_desktop)
        else:
            print("✗ Could not switch to server desktop")
    
    finally:
        user32.CloseDesktop(hidden_desktop)

def main():
    """Run all checks on server's hidden desktop"""
    check_server_hidden_desktop()
    test_print_window_on_server_desktop()
    
    print("\n" + "=" * 40)
    print("SERVER DESKTOP CHECK SUMMARY:")
    print("=" * 40)
    print("If windows are found on the server's hidden desktop and")
    print("PrintWindow works, then the capture should work properly.")

if __name__ == '__main__':
    main()
