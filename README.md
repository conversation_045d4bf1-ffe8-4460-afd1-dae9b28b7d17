# Hidden Desktop Remote Control System

A basic client-server implementation for remote desktop control that captures desktop screenshots and handles remote input events.

## Overview

This project implements a simplified version of the hidden desktop concept described in the original README.txt. The system consists of:

1. **Server** (`server.py`) - Captures desktop screenshots and processes remote input events
2. **Client** (`client.py`) - Displays the remote desktop and sends user input back to the server

## Requirements Identified

Based on the existing C++ code and documentation, this system implements:

- **Desktop Image Streaming**: Captures real-time screenshots of the desktop and streams them to connected clients
- **Remote Input Control**: Receives mouse and keyboard events from clients and applies them to the server's desktop
- **Dual-Socket Architecture**: Uses separate connections for desktop images and input events (similar to the original C++ implementation)
- **Real-time Communication**: Provides responsive remote desktop control experience

## Architecture

### Communication Protocol

The system uses a simple TCP-based protocol with two separate connections:

1. **Desktop Connection (Port 4043)**:
   - Server sends screen resolution info on connection
   - Server continuously streams JPEG-compressed screenshots
   - Each image is prefixed with a 4-byte length header

2. **Events Connection (Port 4044)**:
   - <PERSON><PERSON> sends JSON-formatted input events
   - Each event is prefixed with a 4-byte length header
   - <PERSON>s mouse moves, clicks, drags, keyboard input, and scrolling

### Event Types

The client can send the following event types to the server:

```json
// Mouse movement
{"type": "mouse_move", "x": 100, "y": 200}

// Mouse click
{"type": "mouse_click", "x": 100, "y": 200, "button": "left"}

// Mouse drag
{"type": "mouse_drag", "x1": 100, "y1": 200, "x2": 150, "y2": 250, "button": "left"}

// Key press
{"type": "key_press", "key": "enter"}

// Text typing
{"type": "key_type", "text": "Hello World"}

// Mouse scroll
{"type": "scroll", "x": 100, "y": 200, "clicks": 1}
```

## Installation

### Prerequisites

- Python 3.7 or higher
- Windows OS (for pyautogui screen capture and input simulation)

### Install Dependencies

```bash
pip install -r requirements.txt
```

The required packages are:
- `Pillow>=9.0.0` - For image processing and JPEG compression
- `pyautogui>=0.9.54` - For screen capture and input simulation

## Usage

### Running the Server

1. Open a terminal/command prompt
2. Navigate to the project directory
3. Run the server:

```bash
python server.py
```

The server will start and listen on:
- Port 4043 for desktop image streaming
- Port 4044 for input events

You should see output like:
```
2025-07-13 11:12:47,464 - INFO - Starting Hidden Desktop Server on 127.0.0.1
2025-07-13 11:12:47,464 - INFO - Desktop port: 4043, Events port: 4044
2025-07-13 11:12:47,466 - INFO - Desktop server listening on 127.0.0.1:4043
2025-07-13 11:12:47,467 - INFO - Events server listening on 127.0.0.1:4044
```

### Running the Client

1. Open a new terminal/command prompt (keep the server running)
2. Navigate to the project directory
3. Run the client:

```bash
python client.py
```

The client will:
1. Connect to both server ports
2. Open a GUI window displaying the remote desktop
3. Allow you to interact with the remote desktop using mouse and keyboard

You should see output like:
```
2025-07-13 11:13:50,120 - INFO - Starting Hidden Desktop Client
2025-07-13 11:13:50,121 - INFO - Connecting to 127.0.0.1:4043 (desktop) and 127.0.0.1:4044 (events)
2025-07-13 11:13:50,123 - INFO - Connected to desktop server
2025-07-13 11:13:50,125 - INFO - Screen resolution: 1920x1080
2025-07-13 11:13:50,126 - INFO - Connected to events server
```

### Client Controls

- **Mouse Movement**: Move your mouse over the client window to control the remote cursor
- **Left Click**: Click normally to perform left clicks on the remote desktop
- **Right Click**: Right-click to perform right clicks on the remote desktop
- **Mouse Drag**: Click and drag to perform drag operations
- **Keyboard Input**: Type normally to send text to the remote desktop
- **Special Keys**: Function keys, arrow keys, Enter, etc. are supported
- **Mouse Scroll**: Use mouse wheel to scroll on the remote desktop

## Configuration

### Server Configuration

You can modify the server settings by editing the `HiddenDesktopServer` constructor in `server.py`:

```python
def __init__(self, host='127.0.0.1', desktop_port=4043, events_port=4044):
    # Network settings
    self.host = host                    # Server IP address
    self.desktop_port = desktop_port    # Port for desktop streaming
    self.events_port = events_port      # Port for input events
    
    # Screen capture settings
    self.screen_width = 1920           # Capture width
    self.screen_height = 1080          # Capture height
    self.image_quality = 80            # JPEG quality (1-100)
    self.capture_fps = 30              # Target FPS
```

### Client Configuration

You can modify the client settings by editing the `HiddenDesktopClient` constructor in `client.py`:

```python
def __init__(self, host='127.0.0.1', desktop_port=4043, events_port=4044):
    self.host = host                    # Server IP address
    self.desktop_port = desktop_port    # Desktop streaming port
    self.events_port = events_port      # Input events port
```

## Troubleshooting

### Common Issues

1. **"Connection refused" error**: Make sure the server is running before starting the client

2. **Permission errors with pyautogui**: On some systems, you may need to grant accessibility permissions for Python to control mouse and keyboard

3. **High CPU usage**: Reduce the `capture_fps` setting in the server configuration

4. **Poor image quality**: Increase the `image_quality` setting (higher values = better quality but larger file sizes)

5. **Client window too small/large**: The client automatically scales the display window. You can modify the scaling logic in the `_create_gui()` method

### Performance Optimization

- Reduce `capture_fps` for lower CPU usage
- Reduce `image_quality` for faster transmission
- Adjust `screen_width` and `screen_height` for different resolutions

## Differences from Original C++ Implementation

This Python implementation is a simplified version of the original C++ code with the following differences:

1. **No Hidden Desktop**: Uses regular desktop capture instead of creating a hidden Windows desktop
2. **Simplified Protocol**: Uses JSON for events instead of binary protocol
3. **No Compression**: Uses JPEG compression instead of LZNT1 compression
4. **Cross-platform Libraries**: Uses pyautogui instead of Windows-specific APIs
5. **GUI Framework**: Uses tkinter instead of Qt6 (as suggested in README.txt)

## Next Steps

To enhance this implementation:

1. **Add Hidden Desktop Support**: Implement Windows-specific hidden desktop creation
2. **Improve Performance**: Add frame differencing to only send changed regions
3. **Add Security**: Implement authentication and encryption
4. **Better GUI**: Replace tkinter with Qt6 for better performance and features
5. **Multi-client Support**: Allow multiple clients to connect simultaneously
6. **File Transfer**: Add file transfer capabilities
7. **Audio Support**: Add audio streaming capabilities

## License

This is a proof-of-concept implementation for educational purposes.
