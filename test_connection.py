#!/usr/bin/env python3
"""
Test script to verify server-client connectivity
"""

import socket
import json
import struct
import time
import threading

def test_server_connectivity(host='127.0.0.1', desktop_port=4043, events_port=4044):
    """Test basic connectivity to both server ports"""
    
    print("Testing Hidden Desktop Server connectivity...")
    print(f"Server: {host}")
    print(f"Desktop port: {desktop_port}")
    print(f"Events port: {events_port}")
    print("-" * 50)
    
    # Test desktop port
    print("1. Testing desktop port connection...")
    try:
        desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        desktop_socket.settimeout(5)
        desktop_socket.connect((host, desktop_port))
        print("   ✓ Desktop port connection successful")
        
        # Try to receive screen info
        try:
            length_data = desktop_socket.recv(4)
            if len(length_data) == 4:
                data_length = struct.unpack('!I', length_data)[0]
                json_data = desktop_socket.recv(data_length)
                screen_info = json.loads(json_data.decode('utf-8'))
                print(f"   ✓ Received screen info: {screen_info}")
            else:
                print("   ⚠ Could not receive screen info")
        except Exception as e:
            print(f"   ⚠ Error receiving screen info: {e}")
        
        desktop_socket.close()
        
    except Exception as e:
        print(f"   ✗ Desktop port connection failed: {e}")
        return False
    
    # Test events port
    print("2. Testing events port connection...")
    try:
        events_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        events_socket.settimeout(5)
        events_socket.connect((host, events_port))
        print("   ✓ Events port connection successful")
        
        # Try to send a test event
        try:
            test_event = {
                'type': 'mouse_move',
                'x': 100,
                'y': 100
            }
            json_data = json.dumps(test_event).encode('utf-8')
            data_length = len(json_data)
            events_socket.sendall(struct.pack('!I', data_length))
            events_socket.sendall(json_data)
            print("   ✓ Test event sent successfully")
        except Exception as e:
            print(f"   ⚠ Error sending test event: {e}")
        
        events_socket.close()
        
    except Exception as e:
        print(f"   ✗ Events port connection failed: {e}")
        return False
    
    print("-" * 50)
    print("✓ All connectivity tests passed!")
    print("\nServer is ready for client connections.")
    return True

def test_image_reception(host='127.0.0.1', desktop_port=4043, duration=5):
    """Test receiving images from the server for a specified duration"""
    
    print(f"\n3. Testing image reception for {duration} seconds...")
    
    try:
        desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        desktop_socket.connect((host, desktop_port))
        
        # Receive screen info
        length_data = desktop_socket.recv(4)
        data_length = struct.unpack('!I', length_data)[0]
        json_data = desktop_socket.recv(data_length)
        screen_info = json.loads(json_data.decode('utf-8'))
        
        print(f"   Screen info: {screen_info}")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < duration:
            try:
                # Receive image length
                length_data = desktop_socket.recv(4)
                if len(length_data) != 4:
                    break
                    
                img_length = struct.unpack('!I', length_data)[0]
                
                # Receive image data
                img_data = b''
                while len(img_data) < img_length:
                    chunk = desktop_socket.recv(img_length - len(img_data))
                    if not chunk:
                        break
                    img_data += chunk
                
                if len(img_data) == img_length:
                    frame_count += 1
                    if frame_count % 10 == 0:  # Print every 10 frames
                        print(f"   Received frame {frame_count}, size: {len(img_data)} bytes")
                
            except Exception as e:
                print(f"   Error receiving frame: {e}")
                break
        
        desktop_socket.close()
        
        elapsed_time = time.time() - start_time
        fps = frame_count / elapsed_time if elapsed_time > 0 else 0
        
        print(f"   ✓ Received {frame_count} frames in {elapsed_time:.1f} seconds")
        print(f"   ✓ Average FPS: {fps:.1f}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Image reception test failed: {e}")
        return False

if __name__ == '__main__':
    print("Hidden Desktop Server Connectivity Test")
    print("=" * 50)
    
    # Test basic connectivity
    if test_server_connectivity():
        # Test image reception
        test_image_reception()
    else:
        print("\n❌ Basic connectivity failed. Make sure the server is running.")
        print("\nTo start the server, run: python server.py")
    
    print("\n" + "=" * 50)
    print("Test completed.")
