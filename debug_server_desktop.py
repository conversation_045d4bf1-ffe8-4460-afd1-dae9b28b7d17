#!/usr/bin/env python3
"""
Debug the server's hidden desktop to see what windows are there and test PrintWindow
"""

import ctypes
from ctypes import wintypes, windll
import time

def debug_server_desktop():
    """Debug the server's hidden desktop"""
    
    user32 = windll.user32
    gdi32 = windll.gdi32
    kernel32 = windll.kernel32
    
    print("Debugging Server's Hidden Desktop")
    print("=" * 50)
    
    # Use the desktop name from server logs
    desktop_name = "HiddenDesktop_645360e3"  # From the server logs
    print(f"Looking for server desktop: {desktop_name}")
    
    # Try to open the server's desktop
    hidden_desktop = user32.OpenDesktopA(
        desktop_name.encode('ascii'), 0, False, 0x10000000  # GENERIC_ALL
    )
    
    if not hidden_desktop:
        error_code = kernel32.GetLastError()
        print(f"✗ Failed to open server desktop. Error: {error_code}")
        return
    
    print(f"✓ Successfully opened server desktop: {hidden_desktop}")
    
    try:
        # Get current desktop for restoration
        current_thread_id = kernel32.GetCurrentThreadId()
        original_desktop = user32.GetThreadDesktop(current_thread_id)
        
        # Switch to server's hidden desktop
        if user32.SetThreadDesktop(hidden_desktop):
            print("✓ Successfully switched to server's hidden desktop")
            
            # Enumerate windows with detailed info
            windows = []
            
            def enum_windows_proc(hwnd, lParam):
                if user32.IsWindowVisible(hwnd):
                    # Get window title
                    length = user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        user32.GetWindowTextW(hwnd, buffer, length + 1)
                        window_title = buffer.value
                    else:
                        window_title = "(No title)"
                    
                    # Get window rect
                    rect = wintypes.RECT()
                    if user32.GetWindowRect(hwnd, ctypes.byref(rect)):
                        width = rect.right - rect.left
                        height = rect.bottom - rect.top
                        windows.append((hwnd, window_title, width, height, rect))
                    
                return True
            
            # Enumerate windows
            WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            enum_proc = WNDENUMPROC(enum_windows_proc)
            user32.EnumWindows(enum_proc, 0)
            
            print(f"\nFound {len(windows)} windows on server's hidden desktop:")
            for i, (hwnd, title, width, height, rect) in enumerate(windows):
                print(f"{i+1}. {title}")
                print(f"   Handle: {hwnd}")
                print(f"   Size: {width}x{height}")
                print(f"   Position: ({rect.left}, {rect.top}) to ({rect.right}, {rect.bottom})")
                
                # Test PrintWindow on this window
                if width > 0 and height > 0 and width < 2000 and height < 2000:
                    print(f"   Testing PrintWindow...")
                    
                    # Create DCs and bitmap
                    hdc = user32.GetDC(None)
                    if hdc:
                        mem_dc = gdi32.CreateCompatibleDC(hdc)
                        if mem_dc:
                            bitmap = gdi32.CreateCompatibleBitmap(hdc, width, height)
                            if bitmap:
                                old_bitmap = gdi32.SelectObject(mem_dc, bitmap)
                                
                                # Test PrintWindow
                                success = user32.PrintWindow(hwnd, mem_dc, 0)
                                if success:
                                    print(f"   ✓ PrintWindow succeeded")
                                    
                                    # Test if bitmap has content
                                    # Fill with red first
                                    brush = gdi32.CreateSolidBrush(0x0000FF)  # Red
                                    fill_rect = wintypes.RECT(0, 0, width, height)
                                    user32.FillRect(mem_dc, ctypes.byref(fill_rect), brush)
                                    gdi32.DeleteObject(brush)
                                    
                                    # Try PrintWindow again
                                    success2 = user32.PrintWindow(hwnd, mem_dc, 0)
                                    if success2:
                                        print(f"   ✓ PrintWindow with red background succeeded")
                                    else:
                                        print(f"   ✗ PrintWindow with red background failed")
                                else:
                                    print(f"   ✗ PrintWindow failed")
                                
                                gdi32.DeleteObject(bitmap)
                            gdi32.DeleteDC(mem_dc)
                        user32.ReleaseDC(None, hdc)
                else:
                    print(f"   - Skipping PrintWindow test (invalid size)")
                
                print()
            
            # Test desktop background capture
            print("Testing desktop background capture...")
            desktop_hwnd = user32.GetDesktopWindow()
            if desktop_hwnd:
                print(f"Desktop window handle: {desktop_hwnd}")
                
                # Get desktop rect
                desktop_rect = wintypes.RECT()
                if user32.GetWindowRect(desktop_hwnd, ctypes.byref(desktop_rect)):
                    desk_width = desktop_rect.right - desktop_rect.left
                    desk_height = desktop_rect.bottom - desktop_rect.top
                    print(f"Desktop size: {desk_width}x{desk_height}")
                    
                    # Try to capture desktop background
                    hdc = user32.GetDC(None)
                    if hdc:
                        mem_dc = gdi32.CreateCompatibleDC(hdc)
                        if mem_dc:
                            # Create smaller bitmap for testing
                            test_width = min(800, desk_width)
                            test_height = min(600, desk_height)
                            
                            bitmap = gdi32.CreateCompatibleBitmap(hdc, test_width, test_height)
                            if bitmap:
                                gdi32.SelectObject(mem_dc, bitmap)
                                
                                # Fill with blue background
                                brush = gdi32.CreateSolidBrush(0xFF0000)  # Blue
                                fill_rect = wintypes.RECT(0, 0, test_width, test_height)
                                user32.FillRect(mem_dc, ctypes.byref(fill_rect), brush)
                                gdi32.DeleteObject(brush)
                                
                                print(f"✓ Created test desktop bitmap ({test_width}x{test_height})")
                                
                                gdi32.DeleteObject(bitmap)
                            gdi32.DeleteDC(mem_dc)
                        user32.ReleaseDC(None, hdc)
            
            # Switch back
            user32.SetThreadDesktop(original_desktop)
            print("✓ Switched back to original desktop")
            
        else:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to switch to server's hidden desktop. Error: {error_code}")
    
    finally:
        user32.CloseDesktop(hidden_desktop)
        print("✓ Server desktop handle closed")

def main():
    """Run the debug"""
    debug_server_desktop()
    
    print("\n" + "=" * 50)
    print("DEBUG SUMMARY:")
    print("=" * 50)
    print("This shows what windows exist on the server's hidden desktop")
    print("and whether PrintWindow works on them.")
    print("If PrintWindow fails, that explains the black capture.")

if __name__ == '__main__':
    main()
