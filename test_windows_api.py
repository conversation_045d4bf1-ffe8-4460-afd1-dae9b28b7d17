#!/usr/bin/env python3
"""
Test Windows API functionality for hidden desktop
"""

import sys
import ctypes
from ctypes import wintypes, windll
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_windows_api():
    """Test basic Windows API access"""
    print("Testing basic Windows API access...")
    
    try:
        # Test basic user32 access
        user32 = windll.user32
        kernel32 = windll.kernel32
        
        # Get current thread ID
        thread_id = kernel32.GetCurrentThreadId()
        print(f"✓ Current thread ID: {thread_id}")
        
        # Get current desktop
        current_desktop = user32.GetThreadDesktop(thread_id)
        print(f"✓ Current desktop handle: {current_desktop}")
        
        # Get desktop window
        desktop_window = user32.GetDesktopWindow()
        print(f"✓ Desktop window handle: {desktop_window}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic Windows API test failed: {e}")
        return False

def test_desktop_creation():
    """Test hidden desktop creation"""
    print("\nTesting hidden desktop creation...")
    
    try:
        user32 = windll.user32
        kernel32 = windll.kernel32
        
        # Set up function prototypes
        user32.CreateDesktopW.argtypes = [
            wintypes.LPCWSTR, wintypes.LPCWSTR, wintypes.LPCWSTR,
            wintypes.DWORD, wintypes.DWORD, wintypes.LPVOID
        ]
        user32.CreateDesktopW.restype = wintypes.HANDLE
        
        user32.SetThreadDesktop.argtypes = [wintypes.HANDLE]
        user32.SetThreadDesktop.restype = wintypes.BOOL
        
        user32.GetThreadDesktop.argtypes = [wintypes.DWORD]
        user32.GetThreadDesktop.restype = wintypes.HANDLE
        
        user32.CloseDesktop.argtypes = [wintypes.HANDLE]
        user32.CloseDesktop.restype = wintypes.BOOL
        
        # Save original desktop
        thread_id = kernel32.GetCurrentThreadId()
        original_desktop = user32.GetThreadDesktop(thread_id)
        print(f"✓ Original desktop handle: {original_desktop}")
        
        # Create hidden desktop
        desktop_name = "TestHiddenDesktop"
        GENERIC_ALL = 0x10000000
        
        hidden_desktop = user32.CreateDesktopW(
            desktop_name, None, None, 0, GENERIC_ALL, None
        )
        
        if not hidden_desktop:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to create desktop. Error code: {error_code}")
            return False
        
        print(f"✓ Hidden desktop created: {hidden_desktop}")
        
        # Try to switch to hidden desktop
        success = user32.SetThreadDesktop(hidden_desktop)
        if success:
            print("✓ Successfully switched to hidden desktop")
            
            # Switch back to original
            user32.SetThreadDesktop(original_desktop)
            print("✓ Successfully switched back to original desktop")
        else:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to switch to hidden desktop. Error code: {error_code}")
        
        # Clean up
        user32.CloseDesktop(hidden_desktop)
        print("✓ Hidden desktop closed")
        
        return True
        
    except Exception as e:
        print(f"✗ Desktop creation test failed: {e}")
        return False

def test_screen_capture():
    """Test screen capture functionality"""
    print("\nTesting screen capture...")
    
    try:
        user32 = windll.user32
        gdi32 = windll.gdi32
        
        # Set up function prototypes
        user32.GetDesktopWindow.argtypes = []
        user32.GetDesktopWindow.restype = wintypes.HWND
        
        user32.GetDC.argtypes = [wintypes.HWND]
        user32.GetDC.restype = wintypes.HDC
        
        user32.ReleaseDC.argtypes = [wintypes.HWND, wintypes.HDC]
        user32.ReleaseDC.restype = ctypes.c_int
        
        gdi32.CreateCompatibleDC.argtypes = [wintypes.HDC]
        gdi32.CreateCompatibleDC.restype = wintypes.HDC
        
        gdi32.CreateCompatibleBitmap.argtypes = [wintypes.HDC, ctypes.c_int, ctypes.c_int]
        gdi32.CreateCompatibleBitmap.restype = wintypes.HBITMAP
        
        gdi32.DeleteDC.argtypes = [wintypes.HDC]
        gdi32.DeleteDC.restype = wintypes.BOOL
        
        gdi32.DeleteObject.argtypes = [wintypes.HGDIOBJ]
        gdi32.DeleteObject.restype = wintypes.BOOL
        
        # Get desktop window and DC
        desktop_hwnd = user32.GetDesktopWindow()
        if not desktop_hwnd:
            print("✗ Failed to get desktop window")
            return False
        print(f"✓ Desktop window: {desktop_hwnd}")
        
        desktop_dc = user32.GetDC(desktop_hwnd)
        if not desktop_dc:
            print("✗ Failed to get desktop DC")
            return False
        print(f"✓ Desktop DC: {desktop_dc}")
        
        # Create compatible DC
        mem_dc = gdi32.CreateCompatibleDC(desktop_dc)
        if not mem_dc:
            print("✗ Failed to create compatible DC")
            user32.ReleaseDC(desktop_hwnd, desktop_dc)
            return False
        print(f"✓ Memory DC: {mem_dc}")
        
        # Create compatible bitmap
        width, height = 800, 600
        bitmap = gdi32.CreateCompatibleBitmap(desktop_dc, width, height)
        if not bitmap:
            print("✗ Failed to create compatible bitmap")
            gdi32.DeleteDC(mem_dc)
            user32.ReleaseDC(desktop_hwnd, desktop_dc)
            return False
        print(f"✓ Bitmap: {bitmap}")
        
        # Clean up
        gdi32.DeleteObject(bitmap)
        gdi32.DeleteDC(mem_dc)
        user32.ReleaseDC(desktop_hwnd, desktop_dc)
        print("✓ Screen capture test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Screen capture test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Windows API Test Suite for Hidden Desktop")
    print("=" * 50)
    
    if sys.platform != 'win32':
        print("❌ This test requires Windows")
        return
    
    tests = [
        ("Basic Windows API", test_basic_windows_api),
        ("Desktop Creation", test_desktop_creation),
        ("Screen Capture", test_screen_capture)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Windows API is working correctly.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")

if __name__ == '__main__':
    main()
