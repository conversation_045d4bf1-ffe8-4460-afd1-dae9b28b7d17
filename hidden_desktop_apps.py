#!/usr/bin/env python3
"""
Hidden Desktop Application Launcher
Provides functionality to launch various applications on the hidden desktop.
"""

import os
import logging
import winreg
from pathlib import Path

logger = logging.getLogger(__name__)

class HiddenDesktopAppLauncher:
    """Manages application launching on hidden desktop"""
    
    def __init__(self, desktop_manager):
        self.desktop_manager = desktop_manager
        self.launched_apps = []
    
    def launch_explorer(self):
        """Launch Windows Explorer on hidden desktop"""
        try:
            explorer_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'explorer.exe')
            if os.path.exists(explorer_path):
                logger.info("Launching Windows Explorer on hidden desktop")
                pid = self.desktop_manager.launch_application_on_desktop(explorer_path)
                self.launched_apps.append(('explorer', pid))
                return pid
        except Exception as e:
            logger.error(f"Failed to launch Explorer: {e}")
        return None
    
    def launch_chrome(self, url=""):
        """Launch Google Chrome on hidden desktop"""
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                try:
                    args = f"--new-window --no-first-run --no-default-browser-check"
                    if url:
                        args += f' "{url}"'
                    
                    logger.info(f"Launching Chrome on hidden desktop: {chrome_path}")
                    pid = self.desktop_manager.launch_application_on_desktop(chrome_path, args)
                    self.launched_apps.append(('chrome', pid))
                    return pid
                except Exception as e:
                    logger.error(f"Failed to launch Chrome: {e}")
        
        logger.warning("Chrome not found")
        return None
    
    def launch_firefox(self, url=""):
        """Launch Mozilla Firefox on hidden desktop"""
        firefox_paths = [
            "C:\\Program Files\\Mozilla Firefox\\firefox.exe",
            "C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe"
        ]
        
        for firefox_path in firefox_paths:
            if os.path.exists(firefox_path):
                try:
                    args = "--new-window"
                    if url:
                        args += f' "{url}"'
                    
                    logger.info(f"Launching Firefox on hidden desktop: {firefox_path}")
                    pid = self.desktop_manager.launch_application_on_desktop(firefox_path, args)
                    self.launched_apps.append(('firefox', pid))
                    return pid
                except Exception as e:
                    logger.error(f"Failed to launch Firefox: {e}")
        
        logger.warning("Firefox not found")
        return None
    
    def launch_edge(self, url=""):
        """Launch Microsoft Edge on hidden desktop"""
        edge_paths = [
            "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe",
            "C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe"
        ]
        
        for edge_path in edge_paths:
            if os.path.exists(edge_path):
                try:
                    args = "--new-window"
                    if url:
                        args += f' "{url}"'
                    
                    logger.info(f"Launching Edge on hidden desktop: {edge_path}")
                    pid = self.desktop_manager.launch_application_on_desktop(edge_path, args)
                    self.launched_apps.append(('edge', pid))
                    return pid
                except Exception as e:
                    logger.error(f"Failed to launch Edge: {e}")
        
        logger.warning("Edge not found")
        return None
    
    def launch_notepad(self, file_path=""):
        """Launch Notepad on hidden desktop"""
        try:
            notepad_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'notepad.exe')
            args = f'"{file_path}"' if file_path else ""
            
            logger.info("Launching Notepad on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(notepad_path, args)
            self.launched_apps.append(('notepad', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch Notepad: {e}")
        return None
    
    def launch_calculator(self):
        """Launch Calculator on hidden desktop"""
        try:
            calc_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'calc.exe')
            
            logger.info("Launching Calculator on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(calc_path)
            self.launched_apps.append(('calculator', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch Calculator: {e}")
        return None
    
    def launch_cmd(self):
        """Launch Command Prompt on hidden desktop"""
        try:
            cmd_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'cmd.exe')
            
            logger.info("Launching Command Prompt on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(cmd_path)
            self.launched_apps.append(('cmd', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch Command Prompt: {e}")
        return None
    
    def launch_powershell(self):
        """Launch PowerShell on hidden desktop"""
        try:
            ps_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'WindowsPowerShell', 'v1.0', 'powershell.exe')
            
            logger.info("Launching PowerShell on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(ps_path)
            self.launched_apps.append(('powershell', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch PowerShell: {e}")
        return None
    
    def launch_run_dialog(self):
        """Launch Windows Run dialog on hidden desktop"""
        try:
            rundll_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'rundll32.exe')
            args = "shell32.dll,#61"
            
            logger.info("Launching Run dialog on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(rundll_path, args)
            self.launched_apps.append(('run', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch Run dialog: {e}")
        return None
    
    def launch_custom_application(self, app_path, arguments=""):
        """Launch a custom application on hidden desktop"""
        try:
            if not os.path.exists(app_path):
                logger.error(f"Application not found: {app_path}")
                return None
            
            logger.info(f"Launching custom application on hidden desktop: {app_path}")
            pid = self.desktop_manager.launch_application_on_desktop(app_path, arguments)
            self.launched_apps.append(('custom', pid))
            return pid
        except Exception as e:
            logger.error(f"Failed to launch custom application: {e}")
        return None
    
    def find_installed_browsers(self):
        """Find all installed browsers on the system"""
        browsers = []
        
        # Check common browser locations
        browser_checks = [
            ("Chrome", [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
            ]),
            ("Firefox", [
                "C:\\Program Files\\Mozilla Firefox\\firefox.exe",
                "C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe"
            ]),
            ("Edge", [
                "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe",
                "C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe"
            ]),
            ("Internet Explorer", [
                "C:\\Program Files\\Internet Explorer\\iexplore.exe",
                "C:\\Program Files (x86)\\Internet Explorer\\iexplore.exe"
            ])
        ]
        
        for name, paths in browser_checks:
            for path in paths:
                if os.path.exists(path):
                    browsers.append((name, path))
                    break
        
        return browsers
    
    def launch_default_browser(self, url=""):
        """Launch the default browser on hidden desktop"""
        browsers = self.find_installed_browsers()
        
        if not browsers:
            logger.warning("No browsers found")
            return None
        
        # Try to launch the first available browser
        browser_name, browser_path = browsers[0]
        
        try:
            args = ""
            if browser_name == "Chrome":
                args = "--new-window --no-first-run --no-default-browser-check"
            elif browser_name == "Firefox":
                args = "--new-window"
            elif browser_name == "Edge":
                args = "--new-window"
            
            if url:
                args += f' "{url}"'
            
            logger.info(f"Launching default browser ({browser_name}) on hidden desktop")
            pid = self.desktop_manager.launch_application_on_desktop(browser_path, args)
            self.launched_apps.append((browser_name.lower(), pid))
            return pid
            
        except Exception as e:
            logger.error(f"Failed to launch default browser: {e}")
        
        return None
    
    def get_launched_applications(self):
        """Get list of launched applications"""
        return self.launched_apps.copy()
    
    def launch_desktop_shell(self):
        """Launch the Windows desktop shell (taskbar, desktop) on hidden desktop"""
        try:
            # Launch the Windows shell (explorer.exe as shell)
            # This should create the taskbar and desktop environment
            explorer_path = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'explorer.exe')
            if os.path.exists(explorer_path):
                logger.info("Launching Windows Shell (desktop environment) on hidden desktop")
                # Use /desktop flag to create full desktop environment
                pid = self.desktop_manager.launch_application_on_desktop(explorer_path, "/desktop")
                self.launched_apps.append(('shell', pid))
                return pid
        except Exception as e:
            logger.error(f"Failed to launch desktop shell: {e}")
        return None

    def launch_startup_applications(self):
        """Launch a set of default applications for the hidden desktop"""
        logger.info("Launching startup applications on hidden desktop")

        # First launch the desktop shell to get taskbar and desktop environment
        self.launch_desktop_shell()

        # Wait for shell to initialize
        import time
        time.sleep(3)

        # Launch Explorer windows
        self.launch_explorer()

        # Wait a moment for Explorer to initialize
        time.sleep(2)

        # Launch default browser
        self.launch_default_browser()

        # Launch Notepad
        self.launch_notepad()

        # Launch Calculator for testing
        self.launch_calculator()

        logger.info(f"Launched {len(self.launched_apps)} startup applications")
