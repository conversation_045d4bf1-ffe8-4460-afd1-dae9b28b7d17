
- I THINK IT DOEN THIS
* CREATE A DESKTOP AND <PERSON><PERSON><PERSON><PERSON> IMAGES OF THAT NEW DESKTOP AND SEND BACK 
* WE CAN SEND EVENT TO THAT DESKTOP AND IT WILL SSEND THE IMAGE WHICH MAKE IT LOOKS LIKE IT IS BEEN CONTROL BUT THE DESKTOP IS JUST AN IMAGE
* I THINK WE JUST NEED A WINDOW GUI TO RECEIVE AND DISPLAY THIS IMAGE AND THE GUI SIZE WILL BE THE SIZE OF THE DESKTOP CREATED SO WHEN WE SEND EVENT BACK FORM THIS GUI IT WILL SEND TO THE CREATED DESKTOP CORRECTLY

I THINK THAT IS WHAT IT DOES

- YOU CAN ADD COMMENTS TO UNDERSTAND EACH PART OF IT AND SEND ME ADDRESS TO PAY IN 


IT IS LIKE CREATING A NEW DESKTOP IN WINDOWS 11 BUT THIS IS DONE BY USING C++


                                                              updated
- i believe we could use qt6 to develop the gui that receive the image and send event i have done somthing like a gui like that in python you can have an idea how it looks like and do it in c++ 
- the beloe does another job entirely i just show you a sample so you can get an idea how the desktop gui can be done in qt6 c++ since it is advance than that of py we can archive that desktop gui in it 
-  this is just a sample to show you the desktop  so that you can grab some knowledge from it liek the module use the script is very long so i extracted only the main part of it which 
- what i did was create a desktop gui then inside this gui i create an event handling thread to send event and receive event so now i receive the image and show it in the desktop gui the  i control the image by clickign anypart of the desktop then it send this event back to the stuff that sent me that image the pc proof of concept and then see the desktop gui continues to receive image iupdate the image keep receiving and looks like it is changing but it is just event that i sent back that changes it



###  this is the gui that shows up and it read byte
class VirtualDesktopThread(ClientBaseThread):
    open_cellar_door = pyqtSignal(Screen)
    request_screen_selection_dialog_signal = pyqtSignal(list)
    received_dirty_rect_signal = pyqtSignal(QImage, int, int)
    start_events_worker_signal = pyqtSignal()

    def __init__(self, session: Session) -> None:
        super().__init__(session, WorkerKind.Desktop)
        try:
            self.selected_screen: Optional[Screen] = None
            self.event_loop: Optional[QEventLoop] = None
        except Exception as e:
            logger.error(f"Error in VirtualDesktopThread.__init__: {str(e)}")
            raise

    def open_or_refresh_cellar_door(self) -> None:
        try:
            if self.selected_screen is not None:
                self.open_cellar_door.emit(self.selected_screen)
        except Exception as e:
            logger.error(f"Error in open_or_refresh_cellar_door: {str(e)}")

    def client_execute(self) -> None:
        if self.client is None:
            return

        screens_obj = self.client.read_json()
        screens = [Screen(screen) for screen in screens_obj["List"]]
        logger.debug(f"Received {len(screens)} screens from client")

        if len(screens) == 1:
            self.selected_screen = screens[0]
        else:
            self.display_screen_selection_dialog(screens)

        if self.selected_screen is None:
            logger.error("No screen selected, exiting thread")
            return

        self.client.write_json(
            {
                "ScreenName": self.selected_screen.name,
                "ImageCompressionQuality": self.session.option_image_quality,
                "PacketSize": self.session.option_packet_size.value,
                "BlockSize": self.session.option_block_size.value,
            }
        )

        self.open_or_refresh_cellar_door()
        self.start_events_worker_signal.emit()

        packet_max_size = self.session.option_packet_size.value
        self.client.conn.settimeout(None)  # Disable timeout to wait indefinitely

        while self._running:
            data = self.client.conn.recv(13)
            if not data:
                logger.error("Client disconnected (no data), exiting thread")
                break
            if len(data) != 13:
                logger.error("Received incomplete header, skipping")
                continue
            chunk_size, x, y, screen_updated = struct.unpack('IIIB', data)

            if bool(screen_updated):
                self.selected_screen = Screen(self.client.read_json())
                self.open_or_refresh_cellar_door()
                continue

            chunk_bytes = QByteArray()
            bytes_read = 0
            while bytes_read < chunk_size:
                packet_size = min(packet_max_size, chunk_size - bytes_read)
                b = self.client.conn.recv(packet_size)
                if not b:
                    logger.error("Client disconnected while reading chunk, exiting thread")
                    break
                bytes_read += len(b)
                chunk_bytes.append(b)

            if bytes_read != chunk_size:
                logger.error("Incomplete chunk received, skipping")
                continue

            chunk = QImage()
            if not chunk.loadFromData(chunk_bytes):
                logger.error("Failed to load chunk data, skipping")
                continue

            self.received_dirty_rect_signal.emit(chunk, x, y)
                

    def stop(self) -> None:
        try:
            super().stop()
            if self.event_loop is not None:
                self.event_loop.quit()
        except Exception as e:
            logger.error(f"Error in stop: {str(e)}")

    def display_screen_selection_dialog(self, screens: List[Screen]) -> None:
        try:
            self.event_loop = QEventLoop()
            self.request_screen_selection_dialog_signal.emit(screens)
            self.event_loop.exec()
        except Exception as e:
            logger.error(f"Error in display_screen_selection_dialog: {str(e)}")

    @pyqtSlot(Screen)
    def on_screen_selection_dialog_closed(self, screen: Screen) -> None:
        try:
            self.selected_screen = screen
            if self.event_loop is not None:
                self.event_loop.quit()
                self.event_loop = None
        except Exception as e:
            logger.error(f"Error in on_screen_selection_dialog_closed: {str(e)}")


###  this is the eventthread that run inside the above gui that send event to and fro
class EventsThread(ClientBaseThread):
    update_mouse_cursor = pyqtSignal(Qt.CursorShape)
    update_clipboard = pyqtSignal(str)

    def __init__(self, session: Session) -> None:
        super().__init__(session, WorkerKind.Events)

    def client_execute(self) -> None:
        """ Execute the client thread """
        if self.client is None:
            return

        while self._running:
            try:
                event = self.client.read_json()
            except JSONDecodeError:
                continue
            except (OSError, ssl.SSLError, ssl.SSLEOFError):
                break

            if event is None or "Id" not in event:
                continue

            event_id = event["Id"]

            # Handle Cursor Icon Updates and Reflect it on the Virtual Desktop (Native Cursor)
            if event_id == InputEvent.MouseCursorUpdated.value and "Cursor" in event:
                cursor_name = event["Cursor"]

                # Default
                cursor = Qt.CursorShape.ArrowCursor

                if cursor_name in (MouseCursorKind.IDC_SIZEALL.name,
                                   cursor_name == MouseCursorKind.IDC_SIZE.name):
                    cursor = Qt.CursorShape.SizeAllCursor
                elif cursor_name == MouseCursorKind.IDC_SIZENESW.name:
                    cursor = Qt.CursorShape.SizeBDiagCursor
                elif cursor_name == MouseCursorKind.IDC_SIZENS.name:
                    cursor = Qt.CursorShape.SizeVerCursor
                elif cursor_name == MouseCursorKind.IDC_SIZENWSE.name:
                    cursor = Qt.CursorShape.SizeFDiagCursor
                elif cursor_name == MouseCursorKind.IDC_SIZEWE.name:
                    cursor = Qt.CursorShape.SizeHorCursor
                elif cursor_name == MouseCursorKind.IDC_UPARROW.name:
                    cursor = Qt.CursorShape.UpArrowCursor
                elif cursor_name == MouseCursorKind.IDC_WAIT.name:
                    cursor = Qt.CursorShape.WaitCursor
                elif cursor_name == MouseCursorKind.IDC_APPSTARTING.name:
                    cursor = Qt.CursorShape.BusyCursor
                elif cursor_name == MouseCursorKind.IDC_CROSS.name:
                    cursor = Qt.CursorShape.CrossCursor
                elif cursor_name == MouseCursorKind.IDC_HAND.name:
                    cursor = Qt.CursorShape.PointingHandCursor
                elif cursor_name == MouseCursorKind.IDC_HELP.name:
                    cursor = Qt.CursorShape.WhatsThisCursor
                elif cursor_name == MouseCursorKind.IDC_IBEAM.name:
                    cursor = Qt.CursorShape.IBeamCursor
                elif cursor_name == MouseCursorKind.IDC_ICON.name:
                    pass  # Obsolete
                elif cursor_name == MouseCursorKind.IDC_NO.name:
                    cursor = Qt.CursorShape.ForbiddenCursor

                self.update_mouse_cursor.emit(cursor)
            # Handle Clipboard Updates
            elif event_id == InputEvent.ClipboardUpdated.value and "Text" in event:
                if self.session.clipboard_mode in {
                    ClipboardMode.Disabled, ClipboardMode.Send
                }:
                    continue

                self.update_clipboard.emit(event["Text"])

    @pyqtSlot(int, int, MouseState, MouseButton)
    def send_mouse_event(self, x: int, y: int, state: MouseState, button: MouseButton) -> None:
        """ Send mouse event to the server """
        if self.client is not None and self._connected:
            self.client.write_json(
                {
                    "Id": OutputEvent.MouseClickMove.name,
                    "X": x,
                    "Y": y,
                    "Button": button.name,
                    "Type": state.name,
                }
            )

    @pyqtSlot(str)
    def send_key_event(self, keys: str, is_shortcut: bool) -> None:
        """ Send keyboard event to the server """
        if self.client is not None and self._connected:
            self.client.write_json(
                {
                    "Id": OutputEvent.Keyboard.name,
                    "IsShortcut": is_shortcut,
                    "Keys": keys,
                }
            )

    @pyqtSlot(int)
    def send_mouse_wheel_event(self, delta: int) -> None:
        """ Send mouse wheel event to the server """
        if self.client is not None and self._connected:
            self.client.write_json(
                {
                    "Id": OutputEvent.MouseWheel.name,
                    "Delta": delta,
                }
            )

    @pyqtSlot(str)
    def send_clipboard_text(self, text: str) -> None:
        """ Send clipboard text to the server """
        if self.session.clipboard_mode in {
            ClipboardMode.Disabled, ClipboardMode.Receive
        }:
            return

        if self.client is not None and self._connected:
            self.client.write_json(
                {
                    "Id": OutputEvent.ClipboardUpdated.name,
                    "Text": text,
                }
            )

# this is also for event see the keystroke
class TangentUniverse(QGraphicsView):
    def __init__(self) -> None:
        super().__init__()

        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.setMouseTracking(True)

        self.events_thread: Optional[EventsThread] = None
        self.desktop_screen: Optional[Screen] = None

        # instead of doing a simple `setScene(QGraphicsScene()), we will keep a reference to the scene to be updated
        # and avoid slight overhead when calling .scene() method repeatedly.
        self.desktop_scene = QGraphicsScene()
        self.setScene(self.desktop_scene)

        self.clipboard = QApplication.clipboard()
        if self.clipboard is not None:
            self.clipboard.dataChanged.connect(self.clipboard_data_changed)

    def reset_scene(self) -> None:
        if self.desktop_scene is not None:
            self.desktop_scene.clear()

    def set_event_thread(self, events_thread: EventsThread) -> None:
        self.events_thread = events_thread

        self.events_thread.update_mouse_cursor.connect(self.update_mouse_cursor)
        self.events_thread.update_clipboard.connect(self.update_clipboard)

    def set_screen(self, screen: Screen) -> None:
        self.desktop_screen = screen

    def fix_mouse_position(self, x: Union[int, float], y: Union[int, float]) -> Tuple[int, int]:
        """ Fix the virtual desktop mouse position to the original screen position """
        x = int(x)
        y = int(y)

        if self.desktop_screen is None:
            return x, y

        x_ratio = self.desktop_screen.width / self.width()
        y_ratio = self.desktop_screen.height / self.height()

        # We must take in account both virtual desktop size and original screen X, Y position.
        return (self.desktop_screen.x + (x * x_ratio),
                self.desktop_screen.y + (y * y_ratio))

    def send_mouse_event(self, x: Union[int, float], y: Union[int, float], state: MouseState,
                         button: MouseButton) -> None:
        x = int(x)
        y = int(y)

        """ Push mouse event to the events thread """
        if self.events_thread is None:
            return

        self.events_thread.send_mouse_event(
            x,
            y,
            state,
            button
        )

    def mouse_action_handler(self, event: QMouseEvent, is_pressed: bool) -> None:
        """ Handle mouse press and release events """

        if self.events_thread is None:
            return

        pos = event.position()
        x, y = self.fix_mouse_position(pos.x(), pos.y())

        button = event.button()

        mouse_button = {
            Qt.MouseButton.LeftButton: MouseButton.Left,
            Qt.MouseButton.RightButton: MouseButton.Right,
            Qt.MouseButton.MiddleButton: MouseButton.Middle,
        }.get(button, MouseButton.Void)

        self.send_mouse_event(x, y, MouseState.Down if is_pressed else MouseState.Up, mouse_button)

    def mouse_click(self, event: QMouseEvent) -> None:
        """ Simulate mouse click event """
        self.mouse_action_handler(event, True)
        self.mouse_action_handler(event, False)

    def mousePressEvent(self, event: Optional[QMouseEvent]) -> None:
        if event is None:
            return

        self.mouse_action_handler(event, True)

    def mouseReleaseEvent(self, event: Optional[QMouseEvent]) -> None:
        if event is None:
            return

        self.mouse_action_handler(event, False)

    def mouseDoubleClickEvent(self, event: Optional[QMouseEvent]) -> None:
        """ Override mouseDoubleClickEvent method to simulate a remote double click event
        Do something better than this is possible? (cross-platform) """
        if event is None:
            return

        self.mouse_click(event)
        self.mouse_click(event)

    def mouseMoveEvent(self, event: Optional[QMouseEvent]) -> None:
        """ Override mouseMoveEvent method to handle mouse move events """
        if self.events_thread is None or event is None:
            return

        pos = event.position()
        x, y = self.fix_mouse_position(pos.x(), pos.y())

        self.send_mouse_event(x, y, MouseState.Move, MouseButton.Void)

    def clipboard_data_changed(self) -> None:
        """ Handle clipboard data changed event """
        if self.events_thread is None or self.clipboard is None:
            return

        text = self.clipboard.text(QClipboard.Mode.Clipboard)

        self.events_thread.send_clipboard_text(
            text
        )

    @staticmethod
    def parse_f_keys(event: QKeyEvent) -> Optional[str]:
        if event.key() == Qt.Key.Key_F1:
            return "{F1}"
        elif event.key() == Qt.Key.Key_F2:
            return "{F2}"
        elif event.key() == Qt.Key.Key_F3:
            return "{F3}"
        elif event.key() == Qt.Key.Key_F4:
            return "{F4}"
        elif event.key() == Qt.Key.Key_F5:
            return "{F5}"
        elif event.key() == Qt.Key.Key_F6:
            return "{F6}"
        elif event.key() == Qt.Key.Key_F7:
            return "{F7}"
        elif event.key() == Qt.Key.Key_F8:
            return "{F8}"
        elif event.key() == Qt.Key.Key_F9:
            return "{F9}"
        elif event.key() == Qt.Key.Key_F10:
            return "{F10}"
        elif event.key() == Qt.Key.Key_F11:
            return "{F11}"
        elif event.key() == Qt.Key.Key_F12:
            return "{F12}"
        elif event.key() == Qt.Key.Key_F13:
            return "{F13}"
        elif event.key() == Qt.Key.Key_F14:
            return "{F14}"
        elif event.key() == Qt.Key.Key_F15:
            return "{F15}"
        elif event.key() == Qt.Key.Key_F16:
            return "{F16}"
        else:
            return None

    def keyPressEvent(self, event: Optional[QKeyEvent]) -> None:
        """ Override keyPressEvent method to handle key press events
            For a maximum of compatibility, we will not yet use match case from Python > 3.10 to handle such big enum"""
        if self.events_thread is None or event is None:
            return

        if not event.isInputEvent():
            return

        key_text: Optional[str] = None
        is_shortcut = False

        # Handle Ctrl + C, Ctrl + V, Ctrl + X etc.. CTRL + [A-Z]
        if (Qt.Key.Key_A <= event.key() <= Qt.Key.Key_Z) and \
                event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            key_text = "{^}" + chr(event.key())
            is_shortcut = True

        # Handle [F1-F12] and/or ALT + [F1-F12]
        elif Qt.Key.Key_F1 <= event.key() <= Qt.Key.Key_F12:
            key_text = self.parse_f_keys(event)

            if event.modifiers() == Qt.KeyboardModifier.AltModifier and key_text is not None:
                key_text = "{%}" + key_text
                is_shortcut = True

        # Handle WIN + L
        elif (event.key() == Qt.Key.Key_L) and event.modifiers() == Qt.KeyboardModifier.MetaModifier:
            key_text = "{LOCKWORKSTATION}"

        # Reserved for future use
        # Handle Ctrl + Alt + Del
        # elif (event.key() == Qt.Key.Key_Delete and
        #        event.modifiers() == (
        #                Qt.KeyboardModifier.ControlModifier |
        #                Qt.KeyboardModifier.AltModifier
        #        )):
        #    key_text = "{CTRL+ALT+DEL}"

        # Arrow Keys
        elif event.key() == Qt.Key.Key_Up:
            key_text = "{UP}"
        elif event.key() == Qt.Key.Key_Down:
            key_text = "{DOWN}"
        elif event.key() == Qt.Key.Key_Left:
            key_text = "{LEFT}"
        elif event.key() == Qt.Key.Key_Right:
            key_text = "{RIGHT}"

        # Make RETURN (Numpad) key to be the same as ENTER key
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            key_text = "{ENTER}"

        # Other Special Keys
        elif event.key() == Qt.Key.Key_Backspace:
            key_text = "{BACKSPACE}"
        elif event.key() == Qt.Key.Key_Tab:
            key_text = "{TAB}"
        elif event.key() == Qt.Key.Key_Escape:
            key_text = "{ESC}"
        elif event.key() == Qt.Key.Key_CapsLock:
            key_text = "{CAPSLOCK}"
        elif event.key() == Qt.Key.Key_Delete:
            key_text = "{DEL}"
        elif event.key() == Qt.Key.Key_Home:
            key_text = "{HOME}"
        elif event.key() == Qt.Key.Key_End:
            key_text = "{END}"
        elif event.key() == Qt.Key.Key_PageUp:
            key_text = "{PGUP}"
        elif event.key() == Qt.Key.Key_PageDown:
            key_text = "{PGDN}"
        elif event.key() == Qt.Key.Key_Insert:
            key_text = "{INS}"
        elif event.key() == Qt.Key.Key_Help:
            key_text = "{HELP}"
        elif event.key() == Qt.Key.Key_Print:
            key_text = "{PRTSC}"
        elif event.key() == Qt.Key.Key_ScrollLock:
            key_text = "{SCROLLLOCK}"

        # Modifier Keys
        elif event.key() == Qt.Key.Key_Meta:
            key_text = "{!}"
        elif event.key() == Qt.Key.Key_Control:
            pass
        elif event.key() == Qt.Key.Key_Alt:
            pass
        elif event.key() == Qt.Key.Key_Shift:
            pass

        # Special Character to Escape
        elif event.key() == Qt.Key.Key_BraceLeft:
            key_text = "{{"

        else:
            key_text = event.text()

        if key_text is not None:
            self.events_thread.send_key_event(key_text, is_shortcut)

    def wheelEvent(self, event: Optional[QWheelEvent]) -> None:
        """ Override wheelEvent method to handle mouse wheel events """
        if self.events_thread is None or event is None:
            return

        delta = event.angleDelta().y()

        self.events_thread.send_mouse_wheel_event(delta)

    @pyqtSlot(Qt.CursorShape)
    def update_mouse_cursor(self, cursor: Qt.CursorShape) -> None:
        self.setCursor(cursor)

    @pyqtSlot(str)
    def update_clipboard(self, text: str) -> None:
        if self.clipboard is not None:
            self.clipboard.setText(text)


class DesktopWindow(QMainWindow):
    def __init__(self, command_queue: Queue, desktop_port: int, events_port: int):
        super().__init__()
        self.server_address = "0.0.0.0"
        self.desktop_port = desktop_port
        self.events_port = events_port
        self.password = "King2000#"
        self.show_fps = False
        self.command_queue = command_queue
        self.session = Session(self.server_address, self.desktop_port, self.events_port, self.password)
        self.session.clipboard_mode = ClipboardMode.Both
        self.session.option_image_quality = 80
        self.session.option_packet_size = PacketSize.Size4096
        self.session.option_block_size = BlockSize.Size64
        self.session.presentation = False
        self.resize(QSize(640, 360))
        self.setMouseTracking(True)
        self.setContentsMargins(0, 0, 0, 0)
        self.tangent_universe = TangentUniverse()
        self.setCentralWidget(self.tangent_universe)
        self.desktop_graphics_pixmap = None
        self.desktop_pixmap = None
        self.desktop_thread = None
        self.events_thread = None
        if self.show_fps:
            self.FPS_counter = 0
            self.FPS_Elapsed = time.time()
        self.start_desktop_thread()

    def update_fps(self):
        self.FPS_counter += 1
        elapsed = time.time() - self.FPS_Elapsed
        if elapsed >= 1.0:
            self.setWindowTitle(f"{self.windowTitle()} - FPS: {self.FPS_counter}")
            self.FPS_counter = 0
            self.FPS_Elapsed = time.time()

    def thread_finished(self, on_error: bool) -> None:
        if on_error:
            logger.error("Thread finished with error")

    def start_desktop_thread(self) -> None:
        self.stop_desktop_thread()
        self.desktop_thread = VirtualDesktopThread(self.session)
        self.desktop_thread.received_dirty_rect_signal.connect(self.update_scene, Qt.ConnectionType.QueuedConnection)
        self.desktop_thread.open_cellar_door.connect(self.open_cellar_door, Qt.ConnectionType.QueuedConnection)
        self.desktop_thread.thread_finished.connect(self.thread_finished, Qt.ConnectionType.QueuedConnection)
        self.desktop_thread.request_screen_selection_dialog_signal.connect(self.display_screen_selection_dialog, Qt.ConnectionType.QueuedConnection)
        self.desktop_thread.start_events_worker_signal.connect(self.start_events_thread, Qt.ConnectionType.QueuedConnection)
        self.desktop_thread.start()

    def stop_desktop_thread(self) -> None:
        if self.desktop_thread:
            try:
                self.desktop_thread.received_dirty_rect_signal.disconnect(self.update_scene)
                self.desktop_thread.open_cellar_door.disconnect(self.open_cellar_door)
                self.desktop_thread.thread_finished.disconnect(self.thread_finished)
                self.desktop_thread.request_screen_selection_dialog_signal.disconnect(self.display_screen_selection_dialog)
                self.desktop_thread.start_events_worker_signal.disconnect(self.start_events_thread)
            except TypeError:
                pass
            if self.desktop_thread.isRunning():
                self.desktop_thread.stop()
                self.desktop_thread.wait()
            self.desktop_thread = None

    def start_events_thread(self) -> None:
        self.stop_events_thread()
        if not self.session.presentation:
            self.events_thread = EventsThread(self.session)
            self.events_thread.thread_finished.connect(self.thread_finished, Qt.ConnectionType.QueuedConnection)
            self.events_thread.start()
            self.tangent_universe.set_event_thread(self.events_thread)

    def stop_events_thread(self) -> None:
        if self.events_thread:
            try:
                self.events_thread.thread_finished.disconnect(self.thread_finished)
            except TypeError:
                pass
            if self.events_thread.isRunning():
                self.events_thread.stop()
                self.events_thread.wait()
            self.events_thread = None

    def close_cellar_door(self) -> None:
        self.stop_desktop_thread()
        self.stop_events_thread()

    def showEvent(self, event: Optional[QShowEvent]) -> None:
        super().showEvent(event)
        self.fit_scene()

    def closeEvent(self, event: Optional[QCloseEvent]) -> None:
        self.close_cellar_door()
        if self.command_queue:
            try:
                self.command_queue.put("stop_remote", block=False)  # Non-blocking to avoid stalls
                logger.error("Signaled 'stop_remote' via queue")
            except Queue.Full:
                logger.warning("Command queue full, 'stop_remote' dropped")
        if self.session:
            self.session.end_session()
        kill_processes_by_port(self.desktop_port, local=False)
        kill_processes_by_port(self.events_port, local=False)
        if event:
            event.accept()
        QApplication.quit()  
        sys.exit(0)

    def open_cellar_door(self, screen: Screen) -> None:
        screen = copy.deepcopy(screen)
        self.tangent_universe.reset_scene()
        self.desktop_pixmap = QPixmap(screen.size())
        self.desktop_pixmap.fill(Qt.GlobalColor.black)
        self.desktop_graphics_pixmap = QGraphicsPixmapItem(self.desktop_pixmap)
        self.tangent_universe.desktop_scene.addItem(self.desktop_graphics_pixmap)
        self.tangent_universe.set_screen(screen)
        local_screen = QApplication.primaryScreen()
        if local_screen:
            local_screen_size = local_screen.size()
            remote_screen_width = int(screen.width / local_screen.devicePixelRatio())
            remote_screen_height = int(screen.height / local_screen.devicePixelRatio())
            if local_screen_size.width() <= remote_screen_width or local_screen_size.height() <= remote_screen_height:
                adjust_vertically = local_screen_size.width() > local_screen_size.height()
                if adjust_vertically:
                    new_width = round((local_screen_size.width() * VD_WINDOW_ADJUST_RATIO) / 100)
                    resized_ratio = round(new_width * 100 / remote_screen_width)
                    new_height = round((remote_screen_height * resized_ratio) / 100)
                else:
                    new_height = round((local_screen_size.height() * VD_WINDOW_ADJUST_RATIO) / 100)
                    resized_ratio = round(new_height * 100 / remote_screen_height)
                    new_width = round((remote_screen_width * resized_ratio) / 100)
            else:
                new_width = remote_screen_width
                new_height = remote_screen_height
            self.setGeometry(local_screen.geometry().left() + (local_screen_size.width() - new_width) // 2,
                             local_screen.geometry().top() + (local_screen_size.height() - new_height) // 2,
                             new_width, new_height)
        self.show()

    def fit_scene(self) -> None:
        if self.tangent_universe and self.desktop_graphics_pixmap and self.desktop_pixmap:
            view_rect = self.tangent_universe.frameRect()
            scale_x = view_rect.width() / self.desktop_pixmap.width()
            scale_y = view_rect.height() / self.desktop_pixmap.height()
            transform = QTransform()
            transform.scale(scale_x, scale_y)
            self.tangent_universe.setTransform(transform, False)
            self.tangent_universe.setSceneRect(0, 0, self.desktop_pixmap.width(), self.desktop_pixmap.height())
            self.update()

    def update_scene(self, chunk: QImage, x: int, y: int) -> None:
        if not self.desktop_pixmap or not self.desktop_graphics_pixmap or not chunk:
            return
        dirty_rect = QRect(x, y, chunk.width(), chunk.height())
        painter = QPainter(self.desktop_pixmap)
        painter.setClipRect(dirty_rect)
        painter.drawImage(dirty_rect, chunk)
        painter.end()
        self.desktop_graphics_pixmap.setPixmap(self.desktop_pixmap)
        self.desktop_graphics_pixmap.update(QRectF(dirty_rect))
        self.fit_scene()

    def resizeEvent(self, event: Optional[QResizeEvent]) -> None:
        self.fit_scene()
        super().resizeEvent(event)

    def screen_selection_rejected(self) -> None:
        self.close()

    @pyqtSlot(list)
    def display_screen_selection_dialog(self, screens: List[Screen]) -> None:
        screen_selection_dialog = ScreenSelectionDialog(self, screens)
        screen_selection_dialog.accepted.connect(lambda: self.desktop_thread.on_screen_selection_dialog_closed(
            screen_selection_dialog.get_selected_screen()) if self.desktop_thread else None)
        screen_selection_dialog.rejected.connect(self.screen_selection_rejected)
        screen_selection_dialog.exec()
		
		