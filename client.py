#!/usr/bin/env python3
"""
Hidden Desktop Client - Simplified Implementation
Connects to server, displays desktop images, and sends input events.
"""

import socket
import threading
import json
import struct
import io
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HiddenDesktopClient:
    def __init__(self, host='127.0.0.1', desktop_port=4043, events_port=4044):
        self.host = host
        self.desktop_port = desktop_port
        self.events_port = events_port
        
        self.desktop_socket = None
        self.events_socket = None
        self.running = False
        
        # GUI components
        self.root = None
        self.canvas = None
        self.current_image = None
        
        # Screen info from server
        self.screen_width = 1920
        self.screen_height = 1080
        
        # Mouse tracking
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.mouse_pressed = False
        
    def start(self):
        """Start the client application"""
        logger.info(f"Starting Hidden Desktop Client")
        logger.info(f"Connecting to {self.host}:{self.desktop_port} (desktop) and {self.host}:{self.events_port} (events)")
        
        # Connect to server
        if not self._connect_to_server():
            return False
            
        self.running = True
        
        # Start desktop receiver thread
        desktop_thread = threading.Thread(target=self._receive_desktop_stream, daemon=True)
        desktop_thread.start()
        
        # Create and start GUI
        self._create_gui()
        
        return True
    
    def stop(self):
        """Stop the client"""
        self.running = False
        if self.desktop_socket:
            self.desktop_socket.close()
        if self.events_socket:
            self.events_socket.close()
        if self.root:
            self.root.quit()
    
    def _connect_to_server(self):
        """Connect to both desktop and events servers"""
        try:
            # Connect to desktop server
            self.desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.desktop_socket.connect((self.host, self.desktop_port))
            logger.info("Connected to desktop server")
            
            # Receive screen info
            screen_info = self._receive_json(self.desktop_socket)
            if screen_info:
                self.screen_width = screen_info.get('width', 1920)
                self.screen_height = screen_info.get('height', 1080)
                logger.info(f"Screen resolution: {self.screen_width}x{self.screen_height}")
            
            # Connect to events server
            self.events_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.events_socket.connect((self.host, self.events_port))
            logger.info("Connected to events server")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to server: {e}")
            messagebox.showerror("Connection Error", f"Failed to connect to server: {e}")
            return False
    
    def _receive_desktop_stream(self):
        """Receive and process desktop image stream"""
        try:
            while self.running:
                try:
                    # Receive image data
                    img_data = self._receive_image_data(self.desktop_socket)
                    if not img_data:
                        break
                    
                    # Convert to PIL Image
                    img_buffer = io.BytesIO(img_data)
                    pil_image = Image.open(img_buffer)
                    
                    # Update GUI with new image
                    if self.root:
                        self.root.after(0, self._update_display, pil_image)
                    
                except Exception as e:
                    logger.error(f"Error receiving desktop stream: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Desktop stream error: {e}")
        finally:
            logger.info("Desktop stream ended")
    
    def _create_gui(self):
        """Create the GUI window"""
        self.root = tk.Tk()
        self.root.title("Hidden Desktop Client")
        self.root.protocol("WM_DELETE_WINDOW", self.stop)
        
        # Calculate window size (scale down if too large)
        max_width = 1200
        max_height = 800
        
        if self.screen_width > max_width or self.screen_height > max_height:
            scale_x = max_width / self.screen_width
            scale_y = max_height / self.screen_height
            scale = min(scale_x, scale_y)
            
            window_width = int(self.screen_width * scale)
            window_height = int(self.screen_height * scale)
        else:
            window_width = self.screen_width
            window_height = self.screen_height
        
        # Create canvas for displaying desktop
        self.canvas = tk.Canvas(
            self.root, 
            width=window_width, 
            height=window_height,
            bg='black'
        )
        self.canvas.pack()
        
        # Bind mouse and keyboard events
        self.canvas.bind('<Motion>', self._on_mouse_move)
        self.canvas.bind('<Button-1>', self._on_mouse_click)
        self.canvas.bind('<Button-3>', self._on_mouse_right_click)
        self.canvas.bind('<ButtonPress-1>', self._on_mouse_press)
        self.canvas.bind('<ButtonRelease-1>', self._on_mouse_release)
        self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
        self.canvas.bind('<MouseWheel>', self._on_mouse_scroll)
        
        self.canvas.focus_set()  # Allow canvas to receive keyboard events
        self.canvas.bind('<KeyPress>', self._on_key_press)
        
        # Center window on screen
        self.root.geometry(f"{window_width}x{window_height}")
        self.root.resizable(False, False)
        
        # Start GUI main loop
        self.root.mainloop()
    
    def _update_display(self, pil_image):
        """Update the display with a new image"""
        try:
            # Resize image to fit canvas if needed
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:  # Canvas is initialized
                if pil_image.size != (canvas_width, canvas_height):
                    pil_image = pil_image.resize((canvas_width, canvas_height), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage and display
                self.current_image = ImageTk.PhotoImage(pil_image)
                self.canvas.delete("all")
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_image)
                
        except Exception as e:
            logger.error(f"Error updating display: {e}")
    
    def _scale_coordinates(self, x, y):
        """Scale canvas coordinates to server screen coordinates"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width > 0 and canvas_height > 0:
            scale_x = self.screen_width / canvas_width
            scale_y = self.screen_height / canvas_height
            
            server_x = int(x * scale_x)
            server_y = int(y * scale_y)
            
            return server_x, server_y
        
        return x, y
    
    def _send_event(self, event_data):
        """Send input event to server"""
        try:
            if self.events_socket:
                self._send_json(self.events_socket, event_data)
        except Exception as e:
            logger.error(f"Error sending event: {e}")
    
    def _on_mouse_move(self, event):
        """Handle mouse move events"""
        server_x, server_y = self._scale_coordinates(event.x, event.y)
        self.last_mouse_x, self.last_mouse_y = server_x, server_y
        
        self._send_event({
            'type': 'mouse_move',
            'x': server_x,
            'y': server_y
        })
    
    def _on_mouse_click(self, event):
        """Handle left mouse click"""
        server_x, server_y = self._scale_coordinates(event.x, event.y)
        
        self._send_event({
            'type': 'mouse_click',
            'x': server_x,
            'y': server_y,
            'button': 'left'
        })
    
    def _on_mouse_right_click(self, event):
        """Handle right mouse click"""
        server_x, server_y = self._scale_coordinates(event.x, event.y)
        
        self._send_event({
            'type': 'mouse_click',
            'x': server_x,
            'y': server_y,
            'button': 'right'
        })
    
    def _on_mouse_press(self, event):
        """Handle mouse button press"""
        self.mouse_pressed = True
        server_x, server_y = self._scale_coordinates(event.x, event.y)
        self.last_mouse_x, self.last_mouse_y = server_x, server_y
    
    def _on_mouse_release(self, event):
        """Handle mouse button release"""
        self.mouse_pressed = False
    
    def _on_mouse_drag(self, event):
        """Handle mouse drag events"""
        if self.mouse_pressed:
            server_x, server_y = self._scale_coordinates(event.x, event.y)
            
            self._send_event({
                'type': 'mouse_drag',
                'x1': self.last_mouse_x,
                'y1': self.last_mouse_y,
                'x2': server_x,
                'y2': server_y,
                'button': 'left'
            })
            
            self.last_mouse_x, self.last_mouse_y = server_x, server_y
    
    def _on_mouse_scroll(self, event):
        """Handle mouse scroll events"""
        server_x, server_y = self._scale_coordinates(event.x, event.y)
        clicks = 1 if event.delta > 0 else -1
        
        self._send_event({
            'type': 'scroll',
            'x': server_x,
            'y': server_y,
            'clicks': clicks
        })
    
    def _on_key_press(self, event):
        """Handle keyboard events"""
        key = event.keysym
        
        # Handle special keys
        if len(key) == 1:
            # Regular character
            self._send_event({
                'type': 'key_type',
                'text': key
            })
        else:
            # Special key
            self._send_event({
                'type': 'key_press',
                'key': key.lower()
            })
    
    def _send_json(self, socket, data):
        """Send JSON data over socket"""
        json_data = json.dumps(data).encode('utf-8')
        data_length = len(json_data)
        socket.sendall(struct.pack('!I', data_length))
        socket.sendall(json_data)
    
    def _receive_json(self, socket):
        """Receive JSON data from socket"""
        # First receive the length
        length_data = socket.recv(4)
        if len(length_data) != 4:
            return None
        data_length = struct.unpack('!I', length_data)[0]
        
        # Then receive the JSON data
        json_data = b''
        while len(json_data) < data_length:
            chunk = socket.recv(data_length - len(json_data))
            if not chunk:
                return None
            json_data += chunk
        
        return json.loads(json_data.decode('utf-8'))
    
    def _receive_image_data(self, socket):
        """Receive image data from socket"""
        # First receive the length
        length_data = socket.recv(4)
        if len(length_data) != 4:
            return None
        data_length = struct.unpack('!I', length_data)[0]
        
        # Then receive the image data
        img_data = b''
        while len(img_data) < data_length:
            chunk = socket.recv(data_length - len(img_data))
            if not chunk:
                return None
            img_data += chunk
        
        return img_data

if __name__ == '__main__':
    client = HiddenDesktopClient()
    client.start()
