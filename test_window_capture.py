#!/usr/bin/env python3
"""
Test the window capture functionality to verify what windows are being captured
"""

import socket
import json
import struct
import time
from PIL import Image
import io

def receive_json(sock):
    """Receive JSON data from socket"""
    # Receive length
    length_data = sock.recv(4)
    if len(length_data) != 4:
        return None
    
    data_length = struct.unpack('!I', length_data)[0]
    
    # Receive JSON data
    json_data = b''
    while len(json_data) < data_length:
        chunk = sock.recv(data_length - len(json_data))
        if not chunk:
            return None
        json_data += chunk
    
    return json.loads(json_data.decode('utf-8'))

def receive_image_data(sock):
    """Receive image data from socket"""
    # Receive length
    length_data = sock.recv(4)
    if len(length_data) != 4:
        return None
    
    data_length = struct.unpack('!I', length_data)[0]
    
    # Receive image data
    image_data = b''
    while len(image_data) < data_length:
        chunk = sock.recv(data_length - len(image_data))
        if not chunk:
            return None
        image_data += chunk
    
    return image_data

def test_window_capture():
    """Test capturing a single frame and save it to verify window content"""
    print("Testing window capture from hidden desktop...")
    
    try:
        # Connect to desktop server
        desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        desktop_socket.connect(('127.0.0.1', 4043))
        print("✓ Connected to desktop server")
        
        # Receive screen info
        screen_info = receive_json(desktop_socket)
        if screen_info:
            print(f"✓ Screen resolution: {screen_info['width']}x{screen_info['height']}")
        
        # Capture a few frames to test
        for frame_num in range(3):
            print(f"\nCapturing frame {frame_num + 1}...")
            
            # Receive image data
            image_data = receive_image_data(desktop_socket)
            
            if image_data:
                # Save the image
                filename = f"hidden_desktop_frame_{frame_num + 1}.jpg"
                with open(filename, 'wb') as f:
                    f.write(image_data)
                
                # Also load with PIL to get info
                try:
                    image = Image.open(io.BytesIO(image_data))
                    print(f"✓ Frame {frame_num + 1} captured: {image.size[0]}x{image.size[1]} pixels")
                    print(f"  Saved as: {filename}")
                    
                    # Analyze image content
                    # Convert to RGB if needed
                    if image.mode != 'RGB':
                        image = image.convert('RGB')
                    
                    # Get some pixel samples to check if it's not just black
                    pixels = list(image.getdata())
                    non_black_pixels = sum(1 for r, g, b in pixels if r > 10 or g > 10 or b > 10)
                    total_pixels = len(pixels)
                    non_black_percentage = (non_black_pixels / total_pixels) * 100
                    
                    print(f"  Non-black pixels: {non_black_percentage:.1f}% ({non_black_pixels}/{total_pixels})")
                    
                    if non_black_percentage > 5:
                        print("  ✓ Image contains significant content (likely real windows)")
                    else:
                        print("  ⚠ Image is mostly black (may be empty desktop or capture issue)")
                    
                except Exception as e:
                    print(f"  ✗ Error analyzing image: {e}")
            else:
                print(f"  ✗ Failed to receive frame {frame_num + 1}")
            
            time.sleep(1)  # Wait between frames
        
        desktop_socket.close()
        print("\n✓ Window capture test completed")
        print("\nCheck the saved image files to see the actual hidden desktop content:")
        print("  - hidden_desktop_frame_1.jpg")
        print("  - hidden_desktop_frame_2.jpg") 
        print("  - hidden_desktop_frame_3.jpg")
        
    except Exception as e:
        print(f"✗ Window capture test failed: {e}")

def analyze_captured_frames():
    """Analyze the captured frames to provide insights"""
    print("\n" + "="*60)
    print("HIDDEN DESKTOP CAPTURE ANALYSIS")
    print("="*60)
    
    for frame_num in range(1, 4):
        filename = f"hidden_desktop_frame_{frame_num}.jpg"
        try:
            image = Image.open(filename)
            print(f"\nFrame {frame_num} Analysis:")
            print(f"  File: {filename}")
            print(f"  Size: {image.size[0]}x{image.size[1]} pixels")
            print(f"  Mode: {image.mode}")
            
            # Color analysis
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            pixels = list(image.getdata())
            
            # Count color distribution
            black_pixels = sum(1 for r, g, b in pixels if r < 10 and g < 10 and b < 10)
            white_pixels = sum(1 for r, g, b in pixels if r > 245 and g > 245 and b > 245)
            colored_pixels = len(pixels) - black_pixels - white_pixels
            
            print(f"  Black pixels: {(black_pixels/len(pixels)*100):.1f}%")
            print(f"  White pixels: {(white_pixels/len(pixels)*100):.1f}%")
            print(f"  Colored pixels: {(colored_pixels/len(pixels)*100):.1f}%")
            
            # Determine content type
            if black_pixels > len(pixels) * 0.8:
                print("  Content: Mostly black (empty desktop or capture issue)")
            elif colored_pixels > len(pixels) * 0.1:
                print("  Content: ✓ Contains windows/UI elements (real capture)")
            else:
                print("  Content: Basic desktop background")
                
        except FileNotFoundError:
            print(f"\nFrame {frame_num}: File not found - {filename}")
        except Exception as e:
            print(f"\nFrame {frame_num}: Error analyzing - {e}")

if __name__ == '__main__':
    test_window_capture()
    analyze_captured_frames()
    
    print("\n" + "="*60)
    print("CONCLUSION:")
    print("="*60)
    print("If the captured frames show real window content with taskbars,")
    print("application windows, and UI elements, then the hidden desktop")
    print("window enumeration and capture is working successfully!")
    print("\nThe client should now display the actual hidden desktop")
    print("environment instead of a synthetic representation.")
