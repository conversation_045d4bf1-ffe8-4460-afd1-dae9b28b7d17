#!/usr/bin/env python3
"""
Diagnose hidden desktop issues - check if applications are actually running
and if window enumeration is working
"""

import ctypes
from ctypes import wintypes, windll
import time
import psutil

def check_hidden_desktop_processes():
    """Check what processes are running that might be on hidden desktop"""
    print("Checking for hidden desktop processes...")
    
    # Look for recently started processes
    current_time = time.time()
    recent_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'create_time', 'cmdline']):
        try:
            # Check if process was created in the last 10 minutes
            if current_time - proc.info['create_time'] < 600:  # 10 minutes
                if proc.info['name'].lower() in ['explorer.exe', 'chrome.exe', 'notepad.exe', 'calc.exe']:
                    recent_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"Found {len(recent_processes)} recent target processes:")
    for proc in recent_processes:
        print(f"  - {proc['name']} (PID: {proc['pid']})")
        if proc['cmdline']:
            cmdline = ' '.join(proc['cmdline'])[:100]
            print(f"    Command: {cmdline}")
    
    return recent_processes

def enumerate_all_windows():
    """Enumerate all windows on current desktop"""
    print("\nEnumerating all windows on current desktop...")
    
    user32 = windll.user32
    windows = []
    
    def enum_windows_proc(hwnd, lParam):
        if user32.IsWindowVisible(hwnd):
            length = user32.GetWindowTextLengthW(hwnd)
            if length > 0:
                buffer = ctypes.create_unicode_buffer(length + 1)
                user32.GetWindowTextW(hwnd, buffer, length + 1)
                window_title = buffer.value
                if window_title and len(window_title.strip()) > 0:
                    windows.append((hwnd, window_title))
        return True
    
    # Set up callback
    WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
    enum_proc = WNDENUMPROC(enum_windows_proc)
    
    user32.EnumWindows(enum_proc, 0)
    
    print(f"Found {len(windows)} visible windows:")
    for hwnd, title in windows[:20]:  # Show first 20
        print(f"  - {title} (Handle: {hwnd})")
    
    if len(windows) > 20:
        print(f"  ... and {len(windows) - 20} more windows")
    
    return windows

def test_desktop_creation():
    """Test creating a hidden desktop and switching to it"""
    print("\nTesting hidden desktop creation...")
    
    user32 = windll.user32
    kernel32 = windll.kernel32
    
    # Get current desktop
    current_thread_id = kernel32.GetCurrentThreadId()
    original_desktop = user32.GetThreadDesktop(current_thread_id)
    print(f"Original desktop handle: {original_desktop}")
    
    # Create hidden desktop
    desktop_name = "TestDiagnosticDesktop"
    hidden_desktop = user32.CreateDesktopA(
        desktop_name.encode('ascii'), None, None, 0, 0x10000000, None  # GENERIC_ALL
    )
    
    if hidden_desktop:
        print(f"✓ Hidden desktop created: {hidden_desktop}")
        
        # Try to switch to it
        success = user32.SetThreadDesktop(hidden_desktop)
        if success:
            print("✓ Successfully switched to hidden desktop")
            
            # Enumerate windows on hidden desktop
            print("Enumerating windows on hidden desktop...")
            hidden_windows = []
            
            def enum_hidden_proc(hwnd, lParam):
                if user32.IsWindowVisible(hwnd):
                    length = user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        user32.GetWindowTextW(hwnd, buffer, length + 1)
                        window_title = buffer.value
                        if window_title and len(window_title.strip()) > 0:
                            hidden_windows.append((hwnd, window_title))
                return True
            
            WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            enum_proc = WNDENUMPROC(enum_hidden_proc)
            user32.EnumWindows(enum_proc, 0)
            
            print(f"Found {len(hidden_windows)} windows on hidden desktop:")
            for hwnd, title in hidden_windows:
                print(f"  - {title} (Handle: {hwnd})")
            
            # Switch back
            user32.SetThreadDesktop(original_desktop)
            print("✓ Switched back to original desktop")
        else:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to switch to hidden desktop. Error: {error_code}")
        
        # Clean up
        user32.CloseDesktop(hidden_desktop)
        print("✓ Hidden desktop closed")
    else:
        error_code = kernel32.GetLastError()
        print(f"✗ Failed to create hidden desktop. Error: {error_code}")

def test_print_window():
    """Test PrintWindow functionality on current desktop"""
    print("\nTesting PrintWindow functionality...")
    
    user32 = windll.user32
    gdi32 = windll.gdi32
    
    # Find a window to test with
    test_hwnd = user32.FindWindowA(None, None)  # Get any window
    if not test_hwnd:
        print("✗ No window found to test with")
        return
    
    # Get window title
    length = user32.GetWindowTextLengthW(test_hwnd)
    if length > 0:
        buffer = ctypes.create_unicode_buffer(length + 1)
        user32.GetWindowTextW(test_hwnd, buffer, length + 1)
        window_title = buffer.value
    else:
        window_title = "Unknown"
    
    print(f"Testing PrintWindow on: {window_title} (Handle: {test_hwnd})")
    
    # Get window rect
    rect = wintypes.RECT()
    if not user32.GetWindowRect(test_hwnd, ctypes.byref(rect)):
        print("✗ Failed to get window rect")
        return
    
    width = rect.right - rect.left
    height = rect.bottom - rect.top
    print(f"Window size: {width}x{height}")
    
    # Create DCs and bitmap
    hdc = user32.GetDC(None)
    if not hdc:
        print("✗ Failed to get DC")
        return
    
    try:
        mem_dc = gdi32.CreateCompatibleDC(hdc)
        if not mem_dc:
            print("✗ Failed to create compatible DC")
            return
        
        try:
            bitmap = gdi32.CreateCompatibleBitmap(hdc, width, height)
            if not bitmap:
                print("✗ Failed to create bitmap")
                return
            
            try:
                gdi32.SelectObject(mem_dc, bitmap)
                
                # Test PrintWindow
                success = user32.PrintWindow(test_hwnd, mem_dc, 0)
                if success:
                    print("✓ PrintWindow succeeded")
                else:
                    print("✗ PrintWindow failed")
                
            finally:
                gdi32.DeleteObject(bitmap)
        finally:
            gdi32.DeleteDC(mem_dc)
    finally:
        user32.ReleaseDC(None, hdc)

def main():
    """Run all diagnostic tests"""
    print("Hidden Desktop Diagnostic Tool")
    print("=" * 50)
    
    # Check processes
    processes = check_hidden_desktop_processes()
    
    # Check windows
    windows = enumerate_all_windows()
    
    # Test desktop creation
    test_desktop_creation()
    
    # Test PrintWindow
    test_print_window()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY:")
    print("=" * 50)
    print(f"Recent target processes: {len(processes)}")
    print(f"Visible windows: {len(windows)}")
    print("\nIf processes are running but no windows are visible on hidden desktop,")
    print("this suggests the applications may not be properly isolated.")

if __name__ == '__main__':
    main()
