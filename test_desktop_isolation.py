#!/usr/bin/env python3
"""
Test desktop isolation by creating a hidden desktop and launching a simple application
"""

import ctypes
from ctypes import wintypes, windll
import time

# Constants
GENERIC_ALL = 0x10000000
CREATE_NEW_CONSOLE = 0x00000010

class STARTUPINFO(ctypes.Structure):
    _fields_ = [
        ("cb", wintypes.DWORD),
        ("lpReserved", wintypes.LPWSTR),
        ("lpDesktop", wintypes.LPWSTR),
        ("lpTitle", wintypes.LPWSTR),
        ("dwX", wintypes.DWORD),
        ("dwY", wintypes.DWORD),
        ("dwXSize", wintypes.DWORD),
        ("dwYSize", wintypes.DWORD),
        ("dwXCountChars", wintypes.DWORD),
        ("dwYCountChars", wintypes.DWORD),
        ("dwFillAttribute", wintypes.DWORD),
        ("dwFlags", wintypes.DWORD),
        ("wShowWindow", wintypes.WORD),
        ("cbReserved2", wintypes.WORD),
        ("lpReserved2", ctypes.POINTER(wintypes.BYTE)),
        ("hStdInput", wintypes.HANDLE),
        ("hStdOutput", wintypes.HANDLE),
        ("hStdError", wintypes.HANDLE),
    ]

class PROCESS_INFORMATION(ctypes.Structure):
    _fields_ = [
        ("hProcess", wintypes.HANDLE),
        ("hThread", wintypes.HANDLE),
        ("dwProcessId", wintypes.DWORD),
        ("dwThreadId", wintypes.DWORD),
    ]

def test_desktop_isolation():
    """Test creating hidden desktop and launching application"""
    
    user32 = windll.user32
    kernel32 = windll.kernel32
    
    print("Testing Desktop Isolation")
    print("=" * 40)
    
    # Get current desktop
    current_thread_id = kernel32.GetCurrentThreadId()
    original_desktop = user32.GetThreadDesktop(current_thread_id)
    print(f"Original desktop handle: {original_desktop}")
    
    # Create hidden desktop
    desktop_name = "TestIsolationDesktop"
    print(f"Creating hidden desktop: {desktop_name}")
    
    hidden_desktop = user32.CreateDesktopA(
        desktop_name.encode('ascii'), None, None, 0, GENERIC_ALL, None
    )
    
    if not hidden_desktop:
        error_code = kernel32.GetLastError()
        print(f"✗ Failed to create hidden desktop. Error: {error_code}")
        return
    
    print(f"✓ Hidden desktop created: {hidden_desktop}")
    
    try:
        # Launch notepad on hidden desktop
        print(f"\nLaunching Notepad on hidden desktop...")
        
        startup_info = STARTUPINFO()
        startup_info.cb = ctypes.sizeof(STARTUPINFO)
        startup_info.lpDesktop = ctypes.c_wchar_p(desktop_name)
        
        process_info = PROCESS_INFORMATION()
        
        command_line = "C:\\Windows\\System32\\notepad.exe"
        
        success = kernel32.CreateProcessW(
            None,  # Application name
            command_line,  # Command line
            None,  # Process security attributes
            None,  # Thread security attributes
            False,  # Inherit handles
            CREATE_NEW_CONSOLE,  # Creation flags
            None,  # Environment
            None,  # Current directory
            ctypes.byref(startup_info),
            ctypes.byref(process_info)
        )
        
        if success:
            print(f"✓ Notepad launched successfully (PID: {process_info.dwProcessId})")
            
            # Close handles
            kernel32.CloseHandle(process_info.hProcess)
            kernel32.CloseHandle(process_info.hThread)
            
            # Wait a moment for application to start
            print("Waiting 3 seconds for application to initialize...")
            time.sleep(3)
            
            # Switch to hidden desktop and enumerate windows
            print("\nSwitching to hidden desktop to check for windows...")
            switch_success = user32.SetThreadDesktop(hidden_desktop)
            
            if switch_success:
                print("✓ Successfully switched to hidden desktop")
                
                # Enumerate windows on hidden desktop
                hidden_windows = []
                
                def enum_hidden_proc(hwnd, lParam):
                    if user32.IsWindowVisible(hwnd):
                        length = user32.GetWindowTextLengthW(hwnd)
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            user32.GetWindowTextW(hwnd, buffer, length + 1)
                            window_title = buffer.value
                            if window_title and len(window_title.strip()) > 0:
                                hidden_windows.append((hwnd, window_title))
                    return True
                
                WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
                enum_proc = WNDENUMPROC(enum_hidden_proc)
                user32.EnumWindows(enum_proc, 0)
                
                print(f"Found {len(hidden_windows)} windows on hidden desktop:")
                for hwnd, title in hidden_windows:
                    print(f"  - {title} (Handle: {hwnd})")
                
                if len(hidden_windows) > 0:
                    print("✓ SUCCESS: Application is running on hidden desktop!")
                else:
                    print("✗ FAILURE: No windows found on hidden desktop")
                
                # Switch back to original desktop
                user32.SetThreadDesktop(original_desktop)
                print("✓ Switched back to original desktop")
                
            else:
                error_code = kernel32.GetLastError()
                print(f"✗ Failed to switch to hidden desktop. Error: {error_code}")
            
            # Check if notepad window appears on main desktop
            print("\nChecking if Notepad appears on main desktop...")
            main_windows = []
            
            def enum_main_proc(hwnd, lParam):
                if user32.IsWindowVisible(hwnd):
                    length = user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        user32.GetWindowTextW(hwnd, buffer, length + 1)
                        window_title = buffer.value
                        if window_title and "notepad" in window_title.lower():
                            main_windows.append((hwnd, window_title))
                return True
            
            WNDENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            enum_proc = WNDENUMPROC(enum_main_proc)
            user32.EnumWindows(enum_proc, 0)
            
            print(f"Found {len(main_windows)} Notepad windows on main desktop:")
            for hwnd, title in main_windows:
                print(f"  - {title} (Handle: {hwnd})")
            
            if len(main_windows) > 0:
                print("⚠ WARNING: Notepad appears on main desktop (isolation failed)")
            else:
                print("✓ Good: No Notepad windows on main desktop")
            
        else:
            error_code = kernel32.GetLastError()
            print(f"✗ Failed to launch Notepad. Error: {error_code}")
    
    finally:
        # Clean up
        user32.CloseDesktop(hidden_desktop)
        print("\n✓ Hidden desktop closed")

def main():
    """Run the desktop isolation test"""
    test_desktop_isolation()
    
    print("\n" + "=" * 40)
    print("ISOLATION TEST SUMMARY:")
    print("=" * 40)
    print("If the application appears on the hidden desktop and NOT on")
    print("the main desktop, then isolation is working correctly.")
    print("If it appears on the main desktop, there's an isolation issue.")

if __name__ == '__main__':
    main()
