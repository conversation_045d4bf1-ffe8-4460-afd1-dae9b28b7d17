#!/usr/bin/env python3
"""
Hidden Desktop Server - Windows Implementation
Creates a hidden Windows desktop and provides remote control functionality.
"""

import socket
import threading
import time
import json
import struct
import io
import os
import sys
from PIL import Image
import logging
from windows_desktop_manager import WindowsDesktopManager
from hidden_desktop_apps import HiddenDesktopAppLauncher

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HiddenDesktopServer:
    def __init__(self, host='127.0.0.1', desktop_port=4043, events_port=4044):
        self.host = host
        self.desktop_port = desktop_port
        self.events_port = events_port
        self.running = False
        self.clients = []
        self.desktop_socket = None
        self.events_socket = None
        
        # Screen capture settings
        self.screen_width = 1920
        self.screen_height = 1080
        self.image_quality = 80
        self.capture_fps = 30
        
        # Windows Desktop Manager
        self.desktop_manager = None
        self.app_launcher = None
        self.desktop_thread_started = False
        
    def start(self):
        """Start the hidden desktop server"""
        logger.info(f"Starting Hidden Desktop Server on {self.host}")
        logger.info(f"Desktop port: {self.desktop_port}, Events port: {self.events_port}")
        
        # Check if running on Windows
        if sys.platform != 'win32':
            logger.error("Hidden desktop functionality requires Windows")
            return False
        
        try:
            # Initialize Windows Desktop Manager
            self.desktop_manager = WindowsDesktopManager()
            
            # Create hidden desktop
            logger.info("Creating hidden Windows desktop...")
            self.desktop_manager.create_hidden_desktop()

            # Initialize application launcher
            self.app_launcher = HiddenDesktopAppLauncher(self.desktop_manager)

            # Launch some basic applications on the hidden desktop
            self._launch_default_applications()
            
            self.running = True
            
            # Start desktop server thread
            desktop_thread = threading.Thread(target=self._start_desktop_server, daemon=True)
            desktop_thread.start()
            
            # Start events server thread  
            events_thread = threading.Thread(target=self._start_events_server, daemon=True)
            events_thread.start()
            
            logger.info("Hidden desktop server started successfully")
            
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Shutting down server...")
                self.stop()
                
        except Exception as e:
            logger.error(f"Failed to start hidden desktop server: {e}")
            return False
        
        return True
    
    def stop(self):
        """Stop the server"""
        self.running = False
        if self.desktop_socket:
            self.desktop_socket.close()
        if self.events_socket:
            self.events_socket.close()
        if self.desktop_manager:
            self.desktop_manager.cleanup()
    
    def _launch_default_applications(self):
        """Launch default applications on the hidden desktop"""
        try:
            if self.app_launcher:
                logger.info("Launching startup applications on hidden desktop...")
                self.app_launcher.launch_startup_applications()

        except Exception as e:
            logger.warning(f"Failed to launch some applications: {e}")
    
    def _start_desktop_server(self):
        """Start the desktop image streaming server"""
        self.desktop_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.desktop_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.desktop_socket.bind((self.host, self.desktop_port))
            self.desktop_socket.listen(5)
            logger.info(f"Desktop server listening on {self.host}:{self.desktop_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.desktop_socket.accept()
                    logger.info(f"Desktop client connected from {addr}")
                    
                    # Handle desktop client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_desktop_client, 
                        args=(client_socket, addr),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"Desktop server error: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to start desktop server: {e}")
    
    def _start_events_server(self):
        """Start the input events server"""
        self.events_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.events_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.events_socket.bind((self.host, self.events_port))
            self.events_socket.listen(5)
            logger.info(f"Events server listening on {self.host}:{self.events_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.events_socket.accept()
                    logger.info(f"Events client connected from {addr}")
                    
                    # Handle events client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_events_client,
                        args=(client_socket, addr),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"Events server error: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to start events server: {e}")
    
    def _handle_desktop_client(self, client_socket, addr):
        """Handle desktop image streaming for a client"""
        try:
            # Send initial screen info
            screen_info = {
                'width': self.screen_width,
                'height': self.screen_height,
                'fps': self.capture_fps
            }
            self._send_json(client_socket, screen_info)
            
            last_capture_time = 0
            frame_interval = 1.0 / self.capture_fps
            
            while self.running:
                current_time = time.time()
                if current_time - last_capture_time >= frame_interval:
                    try:
                        # Capture screenshot from hidden desktop
                        screenshot = self._capture_hidden_desktop()
                        
                        if screenshot:
                            # Convert to JPEG bytes
                            img_buffer = io.BytesIO()
                            screenshot.save(img_buffer, format='JPEG', quality=self.image_quality)
                            img_data = img_buffer.getvalue()
                            
                            # Send image data
                            self._send_image_data(client_socket, img_data)
                            
                            last_capture_time = current_time
                        
                    except Exception as e:
                        logger.error(f"Error capturing/sending screenshot: {e}")
                        break
                else:
                    time.sleep(0.001)  # Small sleep to prevent busy waiting
                    
        except Exception as e:
            logger.error(f"Desktop client {addr} error: {e}")
        finally:
            client_socket.close()
            logger.info(f"Desktop client {addr} disconnected")
    
    def _handle_events_client(self, client_socket, addr):
        """Handle input events from a client"""
        try:
            while self.running:
                try:
                    # Receive event data
                    event_data = self._receive_json(client_socket)
                    if not event_data:
                        break
                        
                    self._process_input_event(event_data)
                    
                except Exception as e:
                    logger.error(f"Error processing event from {addr}: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Events client {addr} error: {e}")
        finally:
            client_socket.close()
            logger.info(f"Events client {addr} disconnected")
    
    def _capture_hidden_desktop(self):
        """Capture screenshot from the hidden desktop"""
        try:
            if not self.desktop_manager:
                return None
            
            # Create a separate thread for desktop operations if not already done
            if not self.desktop_thread_started:
                self.desktop_thread_started = True
                # Switch to hidden desktop for this thread
                self.desktop_manager.switch_to_hidden_desktop()
            
            # Capture screenshot
            screenshot = self.desktop_manager.capture_desktop_screenshot(
                self.screen_width, self.screen_height
            )
            
            return screenshot
            
        except Exception as e:
            logger.error(f"Error capturing hidden desktop: {e}")
            return None
    
    def _process_input_event(self, event_data):
        """Process input events and apply them to the hidden desktop"""
        try:
            if self.desktop_manager:
                self.desktop_manager.send_input_to_desktop(event_data)
        except Exception as e:
            logger.error(f"Error processing input event: {e}")
    
    def _send_json(self, socket, data):
        """Send JSON data over socket"""
        json_data = json.dumps(data).encode('utf-8')
        data_length = len(json_data)
        socket.sendall(struct.pack('!I', data_length))
        socket.sendall(json_data)
    
    def _receive_json(self, socket):
        """Receive JSON data from socket"""
        # First receive the length
        length_data = socket.recv(4)
        if len(length_data) != 4:
            return None
        data_length = struct.unpack('!I', length_data)[0]
        
        # Then receive the JSON data
        json_data = b''
        while len(json_data) < data_length:
            chunk = socket.recv(data_length - len(json_data))
            if not chunk:
                return None
            json_data += chunk
        
        return json.loads(json_data.decode('utf-8'))
    
    def _send_image_data(self, socket, img_data):
        """Send image data over socket"""
        data_length = len(img_data)
        socket.sendall(struct.pack('!I', data_length))
        socket.sendall(img_data)

if __name__ == '__main__':
    server = HiddenDesktopServer()
    server.start()
